package com.jd.qf.ai.biz.core.service.chat;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jd.laf.binding.annotation.JsonConverter;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.biz.common.constants.RedisPrefixConstants;
import com.jd.qf.ai.biz.common.dto.MixText;
import com.jd.qf.ai.biz.common.enums.*;
import com.jd.qf.ai.biz.common.utils.TimeUtils;
import com.jd.qf.ai.biz.core.api.chat.ScrmAiChatService;
import com.jd.qf.ai.biz.core.api.chat.bo.*;
import com.jd.qf.ai.biz.core.api.know.qa.KnowledgeBaseService;
import com.jd.qf.ai.biz.core.api.know.qa.bo.KnowledgeBaseIdConfig;
import com.jd.qf.ai.biz.core.converter.Converter;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.*;
import com.jd.qf.ai.biz.infrastructure.dao.po.*;
import com.jd.qf.ai.biz.infrastructure.rpc.chat.AgentChatRpcService;
import com.jd.qf.ai.server.common.pojo.dto.CommonMessage;
import com.jd.qf.ai.server.common.pojo.enums.*;
import com.jd.qf.ai.server.common.pojo.enums.dify.RequestTypeEnum;
import com.jd.qf.ai.server.sdk.request.ChatReq;
import com.jd.qf.ai.server.sdk.request.GeneralIntentReq;
import com.jd.qf.ai.server.sdk.response.GeneralIntentResp;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import com.jdt.open.capability.lock.LockService;
import com.jdt.open.capability.log.annotation.OpenLog;
import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.jd.qf.ai.biz.common.constants.RedisPrefixConstants.ASYNC_REPLY_LOCK;
import static com.jd.qf.ai.biz.common.constants.RedisPrefixConstants.INSERT_AI_RECORD;
import static com.jd.qf.ai.server.common.pojo.constants.ParamConstants.AGENT_ID;
import static com.jd.qf.ai.server.common.pojo.constants.ParamConstants.HISTORY_MESSAGES;

@Service
@Slf4j
public class ScrmAiChatServiceImpl implements ScrmAiChatService {


    @Resource
    AiChatRecordMapper aiChatRecordMapper;

    @LafValue("agentConfig")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, Map<String, Object>> agentConfigMap;

    @Autowired
    private CsChatRecordNewMapper csChatRecordNewMapper;
    @Autowired
    private AgentChatRpcService agentChatRpcService;
    @Autowired
    private OpenRedisClient openRedisClient;
    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private CustMapper custMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private LockService lockService;

    @LafValue("contextQueryConfig")
    @JsonConverter(isSupportGeneric = true)
    private Map<String, Object> contextQueryConfig;
    @Autowired
    private AiKnowQaMapper aiKnowQaMapper;
    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    /**
     * 更新采纳状态
     *
     * @param query 包含更新接受状态所需参数的查询对象
     */
    @Override
    @OpenLog("更新采纳状态")
    public void updateAcceptStatus(UpdateAcceptStatusQuery query) {

        AiChatRecordPo aiChatRecordPo = aiChatRecordMapper.selectByRecordNo(query.getRecordNo());
        if (aiChatRecordPo == null) {
            log.warn("ScrmAiChatServiceImpl - updateAcceptStatus - 记录不存在 - query:{}", query);
            throw new BizException("记录不存在");
        }

        //乐观锁更新
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setRecordNo(query.getRecordNo());
        updatePo.setAcceptStatus(query.getAcceptStatus());
        updatePo.setOldAcceptStatus(AiChatRecordAcceptStatusEnum.NOT_ACCEPTED.getCode());
        log.info("更新采纳状态入参{}", JSON.toJSONString(updatePo));
        int update = aiChatRecordMapper.update(updatePo);
        log.info("更新采纳状态结果{}", update);
    }

    @Override
    @OpenLog("分页查询AI聊天记录")
    public List<AiChatRecordBo> pageQueryRecord(AiChatRecordPageQuery query) {
        AiChatRecordQueryPo queryPo = Converter.INSTANCE.to(query);
        List<AiChatRecordPo> list = aiChatRecordMapper.selectListByLocation(queryPo);
        if (LocationTypeEnum.BEFORE.getCode().equals(query.getLocationType())) {
            //如果是向前翻页,那么需要将列表反转,保证给前端的数据都是按照msgTime由小到大排序的
            list = CollectionUtil.reverse(list);
        }
        return Converter.INSTANCE.toAiBoList(list);
    }

    @Override
    @OpenLog("意图识别")
    @Deprecated
    public Mono<IntentBo> intent(IntentQuery query) {

        //根据项目ID获取项目下的agent配置,后面可做成单独的基础服务
        Map<String, Object> configMap = agentConfigMap.get(query.getProjectId());
        if (MapUtil.isEmpty(configMap)) {
            log.error("未找到项目的Agent配置,项目ID:{}", query.getProjectId());
            return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
        }
        String agentId = (String) configMap.get(AGENT_ID);
        int customerAddWechatLimitDays = (int) configMap.get("customerAddWechatLimitDays");
        int messageContextTimeLimit = (int) configMap.get("messageContextTimeLimit");
        int messageContextLimit = (int) configMap.get("messageContextLimit");

        //查询会话基础信息
        ConversationBaseInfo baseInfo = getBaseInfo(query);
        if (baseInfo == null) {
            log.error("未找到会话基础信息,项目ID:{},员工ID:{},客户ID:{}", query.getProjectId(), query.getSysUserId(), query.getCustId());
            return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
        }

        //判断客户加微时间是否在限制时间内
        DateTime dateTime = DateUtil.offsetDay(baseInfo.getCustCreateTime(), customerAddWechatLimitDays);
        if (dateTime.isBefore(new DateTime())) {
            log.warn("客户加微时间超过{}天,不回复消息", customerAddWechatLimitDays);
            return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
        }

        //todo 补全限流逻辑
//        //限流逻辑-10分钟内3条同样的话去重
//        Mono<IntentBo> limitRes = limitContentValidate(query);
//        if (limitRes != null) {
//            return limitRes;
//        }

        //查询上文
        List<CommonMessage> contextDtoList = queryContextList(query, messageContextTimeLimit, messageContextLimit);

        GeneralIntentReq intentReq = new GeneralIntentReq();
        intentReq.setMessageList(contextDtoList);

        //先同步地插入一条初始化的记录,根据msgId幂等,后续乐观锁更新
        //加锁,防止多个窗口下的并发请求
        String msgRecordId = query.getMsgRecordId();
        String lockKey = INSERT_AI_RECORD + msgRecordId;
        boolean lock = lockService.lock(lockKey, DateUtil.formatDate(query.getMsgTime()), 30 * 1000);
        if (!lock) {
            log.warn("意图识别初始化AI回复,获取锁失败,说明已有其他工作台窗口在处理,消息ID:{}", msgRecordId);
        } else {
            //加锁成功,保证一个msgId仅插入一条记录
            AiChatRecordPo oldRecord = aiChatRecordMapper.selectByMsgRecordId(msgRecordId);
            if (oldRecord == null) {
                AiChatRecordPo insertPo = buildInitPo(query, contextDtoList, baseInfo);
                log.info("意图识别前,插入一条初始化的记录 - insertPo:{}", JSON.toJSONString(insertPo));
                aiChatRecordMapper.insert(insertPo);
            }
        }
        return agentChatRpcService.generalIntent(intentReq)
                .map(resp -> processIntentResult(query, resp, msgRecordId, contextDtoList))
                .doFinally(signalType -> lockService.unlock(lockKey, DateUtil.formatDate(query.getMsgTime())))
                .timeout(Duration.ofSeconds(10))
                .onErrorResume(e -> {
                    log.error("意图识别发生异常,直接返回不回复", e);
                    updateFail(msgRecordId);
                    return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
                });
    }

    @Override
    public Mono<IntentBo> commonIntent(IntentQuery query) {

        String msgRecordId = query.getMsgRecordId();
        //如果是前端请求的意图识别,则在Redis中打个标,标识这条消息前端请求过,后续MQ再请求的时候,便认为处理过了,不再处理
        if (RequestTypeEnum.FRONTEND.getCode().equals(query.getRequestType())) {
            openRedisClient.setEx(RedisPrefixConstants.FRONTEND_REQUEST + msgRecordId, DateUtil.formatDateTime(new Date()), 5);
        }

        //todo 补全限流逻辑
//        //限流逻辑-10分钟内3条同样的话去重
//        Mono<IntentBo> limitRes = limitContentValidate(query);
//        if (limitRes != null) {
//            return limitRes;
//        }

        //查询项目下是否配置过AI能力,如果没有配置过,则直接返回不回复,且不记录AI回复
        int res = aiKnowQaMapper.countByProjectId(query.getProjectId());
        if (!agentConfigMap.containsKey(query.getProjectId()) && res == 0) {
            log.warn("项目未配置AI能力,不回复消息,项目ID:{}", query.getProjectId());
            return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
        }

        //查询会话基础信息
        ConversationBaseInfo baseInfo = getBaseInfo(query);
        if (baseInfo == null) {
            log.error("未找到会话基础信息,项目ID:{},员工ID:{},客户ID:{}", query.getProjectId(), query.getSysUserId(), query.getCustId());
            return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
        }

        //找到查询上下文的配置
        int messageContextTimeLimit = (int) contextQueryConfig.get("messageContextTimeLimit");
        int messageContextLimit = (int) contextQueryConfig.get("messageContextLimit");

        //查询上文
        List<CommonMessage> contextDtoList = queryContextList(query, messageContextTimeLimit, messageContextLimit);

        //意图识别前,插入一条记录
        AiChatRecordPo oldRecord = aiChatRecordMapper.selectByMsgRecordId(msgRecordId);
        if (oldRecord == null) {
            AiChatRecordPo insertPo = buildInitPo(query, contextDtoList, baseInfo);
            log.info("意图识别前,插入一条初始化的记录 - insertPo:{}", JSON.toJSONString(insertPo));
            aiChatRecordMapper.insert(insertPo);
        }

        GeneralIntentReq intentReq = new GeneralIntentReq();
        intentReq.setMessageList(contextDtoList);
        Map<String, Object> params=new HashMap<>();
        params.put("project_id",query.getProjectId());
        params.put("add_wx_time",DateUtil.formatDateTime(baseInfo.getCustCreateTime()));
        //查询一个客户的历史会话状态
        if (FansTypeEnum.FANS.getCode().equals(query.getFansType())){
            AiChatRecordPo lastPo = aiChatRecordMapper.selectLastAiRecord(query.getCustId());
            params.put("pre_state", lastPo == null ? null : lastPo.getSessionState());
        }
        intentReq.setParams(params);
        return agentChatRpcService.generalIntent(intentReq)
                .map(resp -> processIntentResult(query, resp, msgRecordId, contextDtoList))
                .timeout(Duration.ofSeconds(10))
                .onErrorResume(e -> {
                    log.error("意图识别发生异常,直接返回不回复", e);
                    updateFail(msgRecordId);
                    return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
                });
    }

    private  void updateFail(String msgRecordId) {
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setMsgRecordId(msgRecordId);
        updatePo.setOldDataStatus(AiChatDataStatusEnum.INIT.getCode());
        updatePo.setDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
        updatePo.setNoApplyReason("RPC调用异常");
        log.info("RPC调用异常,更新记录状态为不回复 - updatePo:{}", JSON.toJSONString(updatePo));
        aiChatRecordMapper.update(updatePo);
    }

    private IntentBo processIntentResult(IntentQuery query, GeneralIntentResp resp, String msgRecordId, List<CommonMessage> contextDtoList) {

        String result = resp.getIntentType();
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setMsgRecordId(msgRecordId);
        updatePo.setOldDataStatus(AiChatDataStatusEnum.INIT.getCode());
        updatePo.setSessionState(resp.getSessionState());
        updatePo.setAgentId(resp.getAgentId());
        updatePo.setInvokeType(resp.getInvokeType());
        if (IntentTypeEnum.REPLY.getCode().equals(result)) {

            //todo 去掉限流
            //回复的话,先校验是否命中了限流规则
//            IntentBo limitTimesRes = limitTimesValidate(query);
//            if (limitTimesRes == null) {
//
//            } else {
//                //否则要将记录更新为不回复,且返回不回复
//                updatePo.setDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
//                updatePo.setNoApplyReason("触发限流");
//                log.info("意图识别后,更新记录状态为不回复 - updatePo:{}", JSON.toJSONString(updatePo));
//                aiChatRecordMapper.update(updatePo);
//                return limitTimesRes;
//            }
            //没有命中就将记录状态修改为处理中,给前端返回要回复
            updatePo.setDataStatus(AiChatDataStatusEnum.PROCESSING.getCode());
            log.info("意图识别后,更新记录状态为处理中 - updatePo:{}", JSON.toJSONString(updatePo));
            aiChatRecordMapper.update(updatePo);
            return IntentBo.builder()
                    .intentType(IntentTypeEnum.REPLY.getCode())
                    .messageList(contextDtoList)
                    .agentId(resp.getAgentId())
                    .build();
        }

        //如果是不回复,将记录更新为不回复,且返回不回复
        updatePo.setDataStatus(AiChatDataStatusEnum.NO_REPLY.getCode());
        updatePo.setNoApplyReason(IntentTypeEnum.getMsgByCode(result));
        log.info("意图识别后,更新记录状态为不回复 - updatePo:{}", JSON.toJSONString(updatePo));
        aiChatRecordMapper.update(updatePo);
        return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
    }

    private AiChatRecordPo buildInitPo(IntentQuery query, List<CommonMessage> contextDtoList, ConversationBaseInfo baseInfo) {

        //群聊的场景,custId取wxCustId,因为发消息的客户不一定是我们系统的人
        boolean isGroup = FansTypeEnum.GROUP.getCode().equals(query.getFansType());
        String custId = isGroup ? query.getWxCustId() : query.getCustId();

        AiChatRecordPo insertPo = new AiChatRecordPo();
        insertPo.setRecordNo(new Snowflake().nextIdStr());
        insertPo.setCorpId(query.getCorpId());
        insertPo.setProjectId(query.getProjectId());
        insertPo.setProjectName(baseInfo.getProjectName());
        insertPo.setMsgRecordId(query.getMsgRecordId());
        insertPo.setMsgTime(TimeUtils.addCurrentTimeMillis(query.getMsgTime()));
        insertPo.setSysUserId(query.getSysUserId());
        insertPo.setSysUserName(baseInfo.getSysUserName());
        insertPo.setCustId(custId);
        insertPo.setCustName(query.getCustName());
        insertPo.setMsgText(query.getContent());
        insertPo.setDataStatus(AiChatDataStatusEnum.INIT.getCode());
        insertPo.setAcceptStatus(AiChatRecordAcceptStatusEnum.NOT_ACCEPTED.getCode());
        insertPo.setMsgContext(JSON.toJSONString(MapUtil.of(HISTORY_MESSAGES, contextDtoList)));
        insertPo.setValid(1);
        insertPo.setCreator(custId);
        insertPo.setModifier(custId);
        insertPo.setCreatedTime(new DateTime());
        insertPo.setModifiedTime(new DateTime());
        insertPo.setGroupId(query.getGroupId());
        insertPo.setFansType(query.getFansType());
        insertPo.setSessionState(SessionStateEnum.IN_PROGRESS.getCode());
        return insertPo;
    }

    /**
     * 查询会话基础信息
     */
    private ConversationBaseInfo getBaseInfo(IntentQuery query) {

        String custId = query.getCustId();
        String projectId = query.getProjectId();
        Integer sysUserId = query.getSysUserId();
        String wxCustId = query.getWxCustId();
        String groupId = query.getGroupId();
        String fansType = query.getFansType();
        boolean isGroup = FansTypeEnum.GROUP.getCode().equals(fansType);


        ConversationBaseInfo baseInfo = new ConversationBaseInfo();

        //区分群聊还是私聊的场景获取key
        String baseInfoKey;
        if (isGroup) {
            baseInfoKey = RedisPrefixConstants.CHAT_SESSION_INFO + groupId + "_" + wxCustId;
        } else {
            baseInfoKey = RedisPrefixConstants.CHAT_SESSION_INFO + custId;
        }

        String baseInfoValue = openRedisClient.get(baseInfoKey);
        if (StrUtil.isNotBlank(baseInfoValue)) {
            baseInfo = JSON.parseObject(baseInfoValue, ConversationBaseInfo.class);
        } else {
            ProjectPo projectPo = projectMapper.queryByProjectId(projectId);
            if (projectPo == null) {
                log.error("未找到项目信息,项目ID:{}", projectId);
                return null;
            }
            baseInfo.setProjectName(projectPo.getProjectName());
            SysUserPo sysUserPo = sysUserMapper.querySysUserById(sysUserId);
            if (sysUserPo == null) {
                log.error("未找到员工信息,员工ID:{}", sysUserId);
                return null;
            }
            baseInfo.setSysUserName(sysUserPo.getSysUserName());

            //区分群还是私聊的场景查询用户信息
            CustPo custPo;
            if (isGroup) {
                custPo = custMapper.queryByGroupMember(groupId, wxCustId);
            } else {
                custPo = custMapper.queryByCustId(custId);
            }

            if (custPo == null) {
                log.error("未找到客户信息,客户ID:{}", custId);
                return null;
            }
            baseInfo.setCustCreateTime(custPo.getCustCreateTime());
            baseInfo.setCustName(custPo.getCustName());
            openRedisClient.setEx(baseInfoKey, JSON.toJSONString(baseInfo), 60 * 60 * 24);
        }
        return baseInfo;
    }

    private IntentBo limitTimesValidate(IntentQuery query) {

        //1分钟，针对一个客户，只回复有效的10条
        String limitKey = RedisPrefixConstants.MESSAGE_REPLY_LIMIT + query.getCustId();
        String limitValue = openRedisClient.get(limitKey);
        if (limitValue != null) {
            int count = Integer.parseInt(limitValue);
            if (count >= 10) {
                log.warn("客户{}在1分钟内发送了{}条消息,不回复消息", query.getCustId(), count);
                return new IntentBo(IntentTypeEnum.NO_REPLY.getCode());
            } else {
                openRedisClient.incr(limitKey);
            }
        } else {
            openRedisClient.setEx(limitKey, "1", 60);
        }
        return null;
    }

    /**
     * 内容去重
     */
    private Mono<IntentBo> limitContentValidate(IntentQuery query) {

        // 10分钟内3条同样的话去重
        String dedupeKey = RedisPrefixConstants.MESSAGE_REPLY_DEDUPE + query.getCustId() + "_" + SecureUtil.md5(query.getContent());
        String dedupeValue = openRedisClient.get(dedupeKey);
        if (dedupeValue != null) {
            int count = Integer.parseInt(dedupeValue);
            if (count >= 3) {
                log.warn("客户{}在10分钟内发送了{}条相同的消息,不回复消息", query.getCustId(), count);
                return Mono.just(new IntentBo(IntentTypeEnum.NO_REPLY.getCode()));
            } else {
                openRedisClient.incr(dedupeKey);
            }
        } else {
            openRedisClient.setEx(dedupeKey, "1", 60 * 10);
        }
        return null;
    }

    private List<CommonMessage> queryContextList(IntentQuery query, int messageContextTimeLimit, int messageContextLimit) {

        List<CommonMessage> contextDtoList;
        CsChatRecordQueryPo queryPo = new CsChatRecordQueryPo();
        queryPo.setCorpId(query.getCorpId());
        queryPo.setSysUserId(query.getSysUserId());
        queryPo.setCustId(query.getCustId());
        queryPo.setStartMsgTime(DateUtil.offsetDay(query.getMsgTime(), -messageContextTimeLimit));
        queryPo.setEndMsgTime(query.getMsgTime());
        queryPo.setLimit(messageContextLimit);
        queryPo.setMsgTypeList(List.of(WxMsgTypeEnum.TEXT.getWxMsgType(),WxMsgTypeEnum.MIXTEXT.getWxMsgType()));
        queryPo.setGroupId(query.getGroupId());
        queryPo.setFansType(query.getFansType());
        log.info("上文查询入参: {}", JSON.toJSONString(queryPo));
        List<CsChatRecordPo> csChatRecordPos = csChatRecordNewMapper.queryList(queryPo);
        log.info("上文查询结果条数: {}", csChatRecordPos.size());

        //整合上下文信息
        contextDtoList = csChatRecordPos.stream().map(po -> {

            String msgType = po.getMsgType();
            CommonMessage dto = new CommonMessage();
            String msgJson = po.getMsgJson();
            JSONObject jsonObject = JSON.parseObject(msgJson);

            if (WxMsgTypeEnum.MIXTEXT.getWxMsgType().equals(msgType)) {
                String mixTextJson = jsonObject.getString("mixText");
                List<MixText> mixTexts = JSONObject.parseArray(mixTextJson, MixText.class);
                //拼接所有子类型为文本的内容
                String content = mixTexts.stream()
                        .filter(mixText -> mixText.getSubtype() == 0)
                        .map(MixText::getText).collect(Collectors.joining());
                dto.setContent(content);
            }else if (WxMsgTypeEnum.TEXT.getWxMsgType().equals(msgType)) {
                dto.setContent(jsonObject.getString("content"));
            }
            dto.setRole(ChatTypeEnum.getRoleByKey(po.getChatType()));
            return dto;
        }).toList();
        return contextDtoList;
    }

    @Override
    @OpenLog("异步请求AI回复")
    public void asyncReply(AsyncReplyCommand command) {

        //空实现,兼容线上旧的接口

//        IntentQuery query = Converter.INSTANCE.toIntentQuery(command);
//
//        //为了避免多窗口并发请求异步回复,此处加锁
//        String lockKey = ASYNC_REPLY_LOCK + command.getMsgRecordId();
//        boolean lock = lockService.lock(lockKey, DateUtil.formatDate(query.getMsgTime()), 30 * 1000);
//        if (lock) {
//            CompletableFuture.runAsync(() -> {
//                log.info("竞争锁成功,开始异步请求AI回复, msgRecordId:{}", command.getMsgRecordId());
//                this.intent(query).doOnSuccess(intentBo -> {
//                            if (IntentTypeEnum.REPLY.getCode().equals(intentBo.getIntentType())) {
//                                //设置意图识别得到的上下文
//                                command.setMessageList(intentBo.getMessageList());
//                                this.streamReply(Converter.INSTANCE.to(command)).subscribe();
//                            }
//                        })
//                        .doFinally(signalType -> lockService.unlock(lockKey, DateUtil.formatDate(query.getMsgTime())))
//                        .subscribe();
//            });
//        }else {
//            log.info("竞争锁失败,不进行异步请求AI回复, msgRecordId:{}", command.getMsgRecordId());
//        }
    }

    @Override
    @OpenLog("流式请求AI回复")
    public Flux<StreamChatBo> streamReply(ReplyCommand command) {

        //判断同一条消息是否已有记录,如果已有记录是COMPLETED状态,则直接返回
        String msgRecordId = command.getMsgRecordId();
        AiChatRecordPo aiChatRecordPo = aiChatRecordMapper.selectByMsgRecordId(msgRecordId);
        if (aiChatRecordPo == null || AiChatDataStatusEnum.INIT.getCode().equals(aiChatRecordPo.getDataStatus())) {
            log.error("未找到AI聊天记录或状态不正确,消息ID:{},记录为:{}", msgRecordId, JSON.toJSONString(aiChatRecordPo));
            return Flux.empty();
        } else if (AiChatDataStatusEnum.COMPLETED.getCode().equals(aiChatRecordPo.getDataStatus())) {
            //已完成的记录直接返回
            log.info("AI聊天记录已存在且已完成,直接返回,记录为:{}", JSON.toJSONString(aiChatRecordPo));
            return Flux.just(
                    StreamChatBo.builder()
                            .answer(aiChatRecordPo.getAiAnswer())
                            .event(MessageTypeEnum.MESSAGE.getCode())
                            .recordNo(aiChatRecordPo.getRecordNo())
                            .build(),
                    StreamChatBo.builder()
                            .event(MessageTypeEnum.MESSAGE_END.getCode())
                            .recordNo(aiChatRecordPo.getRecordNo())
                            .build());
        }

        //获取agent配置
        Map<String, Object> map = agentConfigMap.get(command.getProjectId());
        if (MapUtil.isEmpty(map)) {
            log.error("流式请求未找到项目的Agent配置,项目ID:{}", command.getProjectId());
            return Flux.empty();
        }

        //这里兼容下旧逻辑,如果记录中没有agentId,则使用DUCC配置项目的agentId
        //invokeType同理,没有的话就按照流式调用;稳定后可以直接用记录中的
        String agentId = StrUtil.isBlank(aiChatRecordPo.getAgentId()) ? (String) map.get(AGENT_ID) : aiChatRecordPo.getAgentId();
        String invokeType = StrUtil.isBlank(aiChatRecordPo.getInvokeType()) ? InvokeTypeEnum.STREAM.getCode() : aiChatRecordPo.getInvokeType();

        //带着上下文去请求Agent服务
        if (InvokeTypeEnum.STREAM.getCode().equals(invokeType)) {
            return streamChat(command, agentId,aiChatRecordPo);
        }else {
            return blockChat(command, agentId,aiChatRecordPo);
        }
    }

    private Flux<StreamChatBo> blockChat(ReplyCommand command, String agentId,AiChatRecordPo po) {
        ChatReq chatReq = new ChatReq();
        chatReq.setAgentType(AgentTypeEnum.SELF_BUILD.getCode());
        chatReq.setAgentId(agentId);
        chatReq.setAllMessage(command.getMessageList());

        //填充业务参数
        Map<String, Object> params = new HashMap<>();
        params.put("is_group", FansTypeEnum.GROUP.getCode().equals(po.getFansType()));
        KnowledgeBaseIdConfig knowledgeBaseIdConfig = knowledgeBaseService.getAiKnowConfigPos(po.getProjectId());
        params.put("question_dataset_id", knowledgeBaseIdConfig.getQuestionId());
        params.put("qa_dataset_id", knowledgeBaseIdConfig.getQaId());
        params.put("agent_id", agentId);

        chatReq.setInputs(params);

        AtomicReference<StringBuilder> fullResponse = new AtomicReference<>(new StringBuilder());
        return agentChatRpcService.chat(chatReq)
                .flatMapMany(resp -> {
                    fullResponse.get().append(resp.getAnswer());
                    return Flux.just(
                            StreamChatBo.builder()
                                    .answer(resp.getAnswer())
                                    .event(MessageTypeEnum.MESSAGE.getCode())
                                    .recordNo(po.getRecordNo())
                                    .build(),
                            StreamChatBo.builder()
                                    .event(MessageTypeEnum.MESSAGE_END.getCode())
                                    .recordNo(po.getRecordNo())
                                    .build()
                    );
                })
                .timeout(Duration.ofSeconds(10))
                .doOnComplete(() -> this.doOnComplete(command, fullResponse.get().toString(), po.getRecordNo()))
                .onErrorResume(Exception.class, e -> {
                    log.error("AI回复发生未知异常", e);
                    updateFail(po.getMsgRecordId());
                    return Flux.empty();
                });
    }

    private Flux<StreamChatBo> streamChat(ReplyCommand command, String agentId, AiChatRecordPo po) {

        String recordNo = po.getRecordNo();

        AtomicReference<StringBuilder> fullResponse = new AtomicReference<>(new StringBuilder());

        ChatReq chatReq = new ChatReq();
        chatReq.setAgentType(AgentTypeEnum.SELF_BUILD.getCode());
        chatReq.setAgentId(agentId);
        chatReq.setAllMessage(command.getMessageList());
        //请求完成后,强制更新记录状态为COMPLETED,覆盖大模型回复
        //在redis中新增AI未读数
        return agentChatRpcService.streamChat(chatReq)
                .timeout(Duration.ofSeconds(20))
                .doOnNext(response -> {
                    if (response != null && response.getAnswer() != null) {
                        fullResponse.get().append(response.getAnswer());
                    }
                })
                .doOnComplete(() -> this.doOnComplete(command, fullResponse.get().toString(), recordNo))
                .map(resp -> {
                    StreamChatBo streamChatBo = Converter.INSTANCE.to(resp);
                    streamChatBo.setRecordNo(recordNo);
                    return streamChatBo;
                }).onErrorResume(e -> {
                    log.error("流式请求AI回复发生异常", e);
                    updateFail(po.getMsgRecordId());
                    return Flux.empty();
                });
    }

    private void doOnComplete(ReplyCommand command, String completeResponse, String recordNo) {
        //请求完成后,强制更新记录状态为COMPLETED,覆盖大模型回复
        log.info("收到完整大模型回复{}", completeResponse);
        AiChatRecordPo updatePo = new AiChatRecordPo();
        updatePo.setRecordNo(recordNo);
        updatePo.setAiAnswer(completeResponse);
        updatePo.setDataStatus(AiChatDataStatusEnum.COMPLETED.getCode());
        log.info("更新记录状态为已完成,updatePo:{}", JSON.toJSONString(updatePo));
        aiChatRecordMapper.update(updatePo);

        //在redis中新增AI未读数
        String unReadKey = getUnReadKey(command.getFansType(), command.getCustId(), command.getGroupId());
        if (StrUtil.isBlank(openRedisClient.get(unReadKey))) {
            openRedisClient.set(unReadKey, "1");
        } else {
            openRedisClient.incr(unReadKey);
        }
    }

    @Override
    public UnReadAiRecordBo queryUnRead(OpUnReadAiRecordCommand req) {
        UnReadAiRecordBo unReadAiRecordBo = new UnReadAiRecordBo();
        String unReadKey = getUnReadKey(req.getFansType(),req.getCustId(),req.getGroupId());
        String unReadValue = openRedisClient.get(unReadKey);
        if (StrUtil.isNotBlank(unReadValue)) {
            unReadAiRecordBo.setCount(Integer.parseInt(unReadValue));
        }
        return unReadAiRecordBo;
    }

    @Override
    public void clearUnRead(OpUnReadAiRecordCommand req) {
        String unReadKey = getUnReadKey(req.getFansType(),req.getCustId(),req.getGroupId());
        openRedisClient.del(unReadKey);
    }

    private  String getUnReadKey(String fansType,String custId,String groupId) {
        String unReadKey = RedisPrefixConstants.AI_UNREAD;
        if (FansTypeEnum.GROUP.getCode().equals(fansType)) {
            unReadKey += groupId;
        } else {
            unReadKey += custId;
        }
        return unReadKey;
    }
}
