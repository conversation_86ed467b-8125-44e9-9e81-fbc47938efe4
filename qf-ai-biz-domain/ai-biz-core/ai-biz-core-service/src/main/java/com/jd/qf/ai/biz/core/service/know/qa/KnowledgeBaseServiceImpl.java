package com.jd.qf.ai.biz.core.service.know.qa;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrFormatter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.jd.laf.config.spring.annotation.LafValue;
import com.jd.qf.ai.biz.core.api.know.qa.KnowledgeBaseService;
import com.jd.qf.ai.biz.core.api.know.qa.bo.*;
import com.jd.qf.ai.biz.core.converter.Converter;
import com.jd.qf.ai.biz.core.api.know.qa.bo.KnowledgeBaseIdConfig;
import com.jd.qf.ai.biz.core.service.know.qa.bo.QARow;
import com.jd.qf.ai.biz.core.service.know.qa.excel.CountExcelListener;
import com.jd.qf.ai.biz.core.service.know.qa.excel.ValidateQaListener;
import com.jd.qf.ai.biz.core.service.know.qa.helper.QADataProcessor;
import com.jd.qf.ai.biz.core.service.know.qa.helper.QAValidator;
import com.jd.qf.ai.biz.core.service.know.qa.helper.RemoteKnowledgeService;
import com.jd.qf.ai.biz.core.service.know.qa.helper.SimilarQuestionBuilder;
import com.jd.qf.ai.biz.core.service.oss.OssService;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.*;
import com.jd.qf.ai.biz.infrastructure.dao.po.*;
import com.jd.qf.ai.biz.infrastructure.rpc.chat.AgentChatRpcService;
import com.jd.qf.ai.server.common.lang.pageutil.DBPageUtils;
import com.jd.qf.ai.server.common.lang.util.FileUtil;
import com.jd.qf.ai.server.common.pojo.enums.AgentTypeEnum;
import com.jd.qf.ai.server.common.pojo.enums.dify.*;
import com.jd.qf.ai.server.common.pojo.enums.DeDuplicateEnum;
import com.jd.qf.ai.server.common.pojo.enums.KnowContentTypeEnum;
import com.jd.qf.ai.server.common.pojo.enums.KnowTypeEnum;
import com.jd.qf.ai.server.common.pojo.page.PageResult;
import com.jd.qf.ai.server.common.pojo.result.ResponseEnum;
import com.jd.qf.ai.server.common.pojo.utils.SequenceNoUtils;
import com.jd.qf.ai.server.sdk.request.ChatReq;
import com.jd.qf.ai.server.sdk.response.ChatResp;
import com.jdt.open.capability.log.annotation.OpenLog;
import com.jdt.open.exception.BizException;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.model.datasets.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.validation.constraints.NotBlank;
import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_FORMAT;

/**
 * 知识库服务实现
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
@Slf4j
@Service
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Autowired
    private AiKnowConfigMapper aiKnowConfigMapper;
    @Autowired
    private AiKnowGroupMapper aiKnowGroupMapper;
    @Autowired
    private AiKnowQaMapper aiKnowQaMapper;
    @Autowired
    private AiKnowSimilarQuestionMapper aiKnowSimilarQuestionMapper;
    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private OssService ossService;
    @Autowired
    private DifyDatasetsClient difyDatasetsClient;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private QAValidator qaValidator;
    @Autowired
    private QADataProcessor qaDataProcessor;
    @Autowired
    private SimilarQuestionBuilder similarQuestionBuilder;
    @Autowired
    private RemoteKnowledgeService remoteKnowledgeService;

    @LafValue("getSimilarQuestionsPrompt")
    private String getSimilarQuestionsPrompt;
    @Autowired
    private AgentChatRpcService agentChatRpcService;


    @Override
    @OpenLog("创建空白知识库")
    public void createKnowledgeBase(CreateKnowledgeBaseCommand req) {
        ProjectPo projectPo = getProjectPo(req.getProjectId());

        List<AiKnowConfigPo> aiKnowConfigPos = aiKnowConfigMapper.selectByProjectId(req.getProjectId());
        if (CollectionUtil.isNotEmpty(aiKnowConfigPos)) {
            log.warn("知识库已创建,无需重复创建");
            return;
        }
        //创建问题知识库
        doCreateKnowledgeBase(req, projectPo, KnowContentTypeEnum.QUESTION);

        //创建QA库
        doCreateKnowledgeBase(req, projectPo, KnowContentTypeEnum.QA);

    }

    /**
     * 创建知识库
     *
     * @param req                 创建知识库命令
     * @param projectPo           项目信息
     * @param knowContentTypeEnum 知识内容类型枚举
     */
    private void doCreateKnowledgeBase(CreateKnowledgeBaseCommand req, ProjectPo projectPo, KnowContentTypeEnum knowContentTypeEnum) {
        DatasetResponse dataset;
        try {
            CreateDatasetRequest createDatasetRequest = new CreateDatasetRequest();
            createDatasetRequest.setName(projectPo.getProjectName() + knowContentTypeEnum.getMsg());
            createDatasetRequest.setIndexingTechnique(IndexTechniqueEnum.HIGH_QUALITY.getCode());
            createDatasetRequest.setPermission(DifyPermissionEnum.ALL.getCode());
            dataset = difyDatasetsClient.createDataset(createDatasetRequest);
        } catch (Exception e) {
            log.error("调用Dify RPC创建知识库失败,入参{}", JSON.toJSONString(req), e);
            throw new BizException("创建知识库失败,请联系研发人员解决");
        }

        String externalKnowId = dataset.getId();
        String contentType = knowContentTypeEnum.getCode();

        AiKnowConfigPo aiKnowConfigPo = new AiKnowConfigPo();
        aiKnowConfigPo.setConfigNo(SequenceNoUtils.getSequenceNo());
        aiKnowConfigPo.setProjectId(req.getProjectId());
        aiKnowConfigPo.setProjectName(projectPo.getProjectName());
        aiKnowConfigPo.setKnowType(KnowTypeEnum.DIFY.getCode());
        aiKnowConfigPo.setContentType(contentType);
        aiKnowConfigPo.setExternalKnowId(externalKnowId);
        aiKnowConfigPo.setCreator(req.getOperator());
        aiKnowConfigMapper.insert(aiKnowConfigPo);
    }

    private ProjectPo getProjectPo(String projectId) {
        ProjectPo projectPo = projectMapper.queryByProjectId(projectId);
        if (projectPo == null) {
            log.warn("项目不存在");
            throw new BizException("项目不存在");
        }
        return projectPo;
    }

    @Override
    @OpenLog("添加单条QA")
    public void addQA(AddQACommand req) {
        // 将单条请求转换为批量请求，复用批量逻辑
        BatchAddQACommand batchCommand = BatchAddQACommand.builder()
                .projectId(req.getProjectId())
                .qaList(List.of(req))
                .operator(req.getOperator())
                .build();
        batchAddQA(batchCommand);
    }

    @Override
    @OpenLog("批量添加QA")
    public void batchAddQA(BatchAddQACommand req) {
        if (CollectionUtil.isEmpty(req.getQaList())) {
            log.warn("批量添加QA列表为空");
            return;
        }

        ProjectPo projectPo = getProjectPo(req.getProjectId());

        // 1. 预处理数据
        List<QAProcessData> processDataList = qaDataProcessor.preprocessBatchAddData(req.getQaList());

        // 2. 批量校验重复问题
        List<String> allQuestionMd5List = qaDataProcessor.extractQuestionMd5List(processDataList);
        List<String> allSimilarQuestionMd5List = qaDataProcessor.extractSimilarQuestionMd5List(processDataList);
        qaValidator.batchValidateQuestions(req.getProjectId(), allQuestionMd5List, allSimilarQuestionMd5List);

        // 3. 构建批量插入数据
        List<AiKnowQaPo> qaPoList = qaDataProcessor.buildBatchInsertQAData(processDataList, projectPo, req.getOperator());
        List<AiKnowSimilarQuestionPo> similarQuestionPoList = qaDataProcessor.buildBatchInsertSimilarQuestionData(
                processDataList, req.getProjectId(), req.getOperator());

        // 4. 批量数据库操作
        performBatchDatabaseInsert(qaPoList, similarQuestionPoList);

        // 5. 批量添加到远程知识库
        KnowledgeBaseIdConfig knowledgeBaseIdConfig = getAiKnowConfigPos(req.getProjectId());
        remoteKnowledgeService.batchAddToRemoteKnowledge(knowledgeBaseIdConfig, processDataList);
    }

    /**
     * 执行批量数据库插入操作
     */
    private void performBatchDatabaseInsert(List<AiKnowQaPo> qaPoList, List<AiKnowSimilarQuestionPo> similarQuestionPoList) {
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            log.info("批量添加QA到本地数据库,数量{}", qaPoList.size());
            if (CollectionUtil.isNotEmpty(qaPoList)) {
                aiKnowQaMapper.batchInsert(qaPoList);
            }
            if (CollectionUtil.isNotEmpty(similarQuestionPoList)) {
                aiKnowSimilarQuestionMapper.batchInsert(similarQuestionPoList);
            }
        });
    }


    @Override
    public KnowledgeBaseIdConfig getAiKnowConfigPos(String projectId) {
        List<AiKnowConfigPo> aiKnowConfigPos = aiKnowConfigMapper.selectByProjectId(projectId);
        if (CollectionUtil.isEmpty(aiKnowConfigPos)) {
            log.error("项目下知识库配置为空,入参{}", JSON.toJSONString(projectId));
            throw new BizException("项目下不存在任何知识库配置");
        }
        KnowledgeBaseIdConfig config = new KnowledgeBaseIdConfig();

        AiKnowConfigPo questionConfig = aiKnowConfigPos.stream()
                .filter(po -> KnowContentTypeEnum.QUESTION.getCode().equals(po.getContentType()))
                .findAny()
                .orElse(new AiKnowConfigPo());
        config.setQuestionId(questionConfig.getExternalKnowId());

        AiKnowConfigPo answerConfig = aiKnowConfigPos.stream()
                .filter(po -> KnowContentTypeEnum.QA.getCode().equals(po.getContentType()))
                .findAny()
                .orElse(new AiKnowConfigPo());
        config.setQaId(answerConfig.getExternalKnowId());

        return config;
    }

    @Override
    public SimilarQuestionBo getSimilarQuestions(GetSimilarQuestionsQuery req) {

        String question = req.getQuestion();
        List<String> similarQuestionList = req.getSimilarQuestionList();

        String prompt = StrFormatter.format(getSimilarQuestionsPrompt, question, JSON.toJSONString(similarQuestionList));
        ChatReq chatReq = new ChatReq();
        chatReq.setAgentType(AgentTypeEnum.CHAT_RHINO.getCode());
        chatReq.setModel("Chatrhino-81B-Pro");
        chatReq.setQuery(prompt);
        ChatResp resp = agentChatRpcService.chat(chatReq).block();
        if (resp != null) {
            SimilarQuestionBo bo = new SimilarQuestionBo();
            bo.setSimilarQuestionList(List.of(resp.getAnswer().split(",")));
            return bo;
        }
        return null;
    }


    @Override
    @OpenLog("查询QA详情")
    public QADetailBo queryQADetail(QueryQADetailQuery req) {

        AiKnowQaPo po = getAiKnowQaPo(req.getProjectId(), req.getQaNo());
        AiKnowGroupPo aiKnowGroupPo = getAiKnowGroupPo(req.getProjectId(), po.getGroupNo());

        List<AiKnowSimilarQuestionPo> similarQuestionPoList = aiKnowSimilarQuestionMapper.selectByQaNo(po.getQaNo());
        QADetailBo bo = Converter.INSTANCE.to(po);
        List<String> similarList = similarQuestionPoList.stream().map(AiKnowSimilarQuestionPo::getSimilarQuestion).toList();
        bo.setSimilarQuestionList(similarList);
        bo.setGroupName(aiKnowGroupPo.getGroupName());
        return bo;
    }

    private AiKnowGroupPo getAiKnowGroupPo(String projectId, String groupNo) {
        AiKnowGroupPo aiKnowGroupPo = aiKnowGroupMapper.selectByGroupNo(projectId, groupNo);
        if (aiKnowGroupPo == null) {
            log.warn("分组不存在");
            throw new BizException("分组不存在");
        }
        return aiKnowGroupPo;
    }

    private AiKnowQaPo getAiKnowQaPo(String projectId, String qaNo) {
        AiKnowQaPo po = aiKnowQaMapper.selectByProjectIdAndQaNo(projectId, qaNo);
        if (po == null) {
            log.warn("QA不存在");
            throw new BizException("问答话术不存在");
        }
        return po;
    }

    @Override
    @OpenLog("编辑单条QA")
    public void editQA(EditQACommand req) {
        // 将单条请求转换为批量请求，复用批量逻辑
        BatchEditQACommand batchCommand = BatchEditQACommand.builder()
                .projectId(req.getProjectId())
                .qaList(List.of(req))
                .operator(req.getOperator())
                .build();
        batchEditQA(batchCommand);
    }

    @Override
    @OpenLog("批量编辑QA")
    public void batchEditQA(BatchEditQACommand req) {
        if (CollectionUtil.isEmpty(req.getQaList())) {
            log.warn("批量编辑QA列表为空");
            return;
        }

        // 1. 批量查询现有QA信息
        List<String> qaNoList = req.getQaList().stream()
                .map(EditQACommand::getQaNo)
                .toList();
        List<AiKnowQaPo> existingQaList = aiKnowQaMapper.selectByQaNoList(req.getProjectId(), qaNoList);
        Map<String, AiKnowQaPo> existingQaMap = existingQaList.stream()
                .collect(Collectors.toMap(AiKnowQaPo::getQaNo, qa -> qa));

        // 2. 预处理编辑数据
        List<QAEditProcessData> processDataList = qaDataProcessor.preprocessBatchEditData(
                req.getQaList(), existingQaMap, req.getOperator());

        // 3. 构建批量更新数据
        List<AiKnowQaPo> updatePoList = qaDataProcessor.buildBatchUpdateQAData(req.getQaList(), req.getOperator());
        List<AiKnowSimilarQuestionPo> similarQuestionPoList = buildBatchEditSimilarQuestions(req);

        // 4. 批量数据库操作
        performBatchDatabaseUpdate(qaNoList, updatePoList, similarQuestionPoList,req.getOperator());

        // 5. 批量更新远程知识库
        KnowledgeBaseIdConfig knowledgeBaseIdConfig = getAiKnowConfigPos(req.getProjectId());
        remoteKnowledgeService.batchUpdateRemoteKnowledge(knowledgeBaseIdConfig, processDataList);
    }

    /**
     * 构建批量编辑的相似问题数据
     */
    private List<AiKnowSimilarQuestionPo> buildBatchEditSimilarQuestions(BatchEditQACommand req) {
        List<AiKnowSimilarQuestionPo> similarQuestionPoList = new ArrayList<>();

        for (EditQACommand editCommand : req.getQaList()) {
            List<AiKnowSimilarQuestionPo> similarQuestions = similarQuestionBuilder.buildSimilarQuestions(
                    req.getProjectId(), editCommand.getQaNo(), editCommand.getSimilarQuestionList(), req.getOperator());
            similarQuestionPoList.addAll(similarQuestions);
        }

        return similarQuestionPoList;
    }

    /**
     * 执行批量数据库更新操作
     */
    private void performBatchDatabaseUpdate(List<String> qaNoList, List<AiKnowQaPo> updatePoList,
                                            List<AiKnowSimilarQuestionPo> similarQuestionPoList, String operator) {
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            log.info("批量编辑QA到本地数据库,数量{}", updatePoList.size());

            // 批量删除旧的相似问题
            if (CollectionUtil.isNotEmpty(qaNoList)) {
                aiKnowSimilarQuestionMapper.logicDeleteByQaNo(qaNoList, operator);
            }

            // 批量更新QA
            for (AiKnowQaPo updatePo : updatePoList) {
                aiKnowQaMapper.updateById(updatePo);
            }

            // 批量插入新的相似问题
            if (CollectionUtil.isNotEmpty(similarQuestionPoList)) {
                aiKnowSimilarQuestionMapper.batchInsert(similarQuestionPoList);
            }
        });
    }

    @Override
    @OpenLog("导入QA")
    public void importQA(ImportQACommand req) {
        String filePath = generateImportFilePath(req.getProjectId());
        File importFile = null;

        try {
            // 1. 下载并校验Excel文件
            importFile = downloadAndValidateExcelFile(req.getFileUrl(), filePath);

            // 2. 解析Excel数据
            ValidateQaListener validateQaListener = parseExcelData(importFile);

            // 3. 处理分组信息
            ProjectPo projectPo = getProjectPo(req.getProjectId());
            Map<String, List<QARow>> groupIdMap = processGroup(req, validateQaListener.getGroupMap(), projectPo);

            // 4. 处理重复问题策略
            Map<String, String> questionAndIdMap = buildQuestionIdMap(req.getProjectId(), validateQaListener.getQuestionSet());
            DeDuplicateEnum deDuplicateEnum = validateDeDuplicateStrategy(req.getDeDuplicateType());

            // 5. 批量处理QA数据
            processBatchQAData(req, groupIdMap, questionAndIdMap, deDuplicateEnum);

        } catch (BizException e) {
            log.warn("导入QA-业务异常", e);
            throw e;
        } catch (ExcelDataConvertException e) {
            log.warn("导入QA-格式转换异常", e);
            throw new BizException(ResponseEnum.PARAM_ERROR.getCode(), "导入QA失败-格式转换异常");
        } catch (Exception e) {
            log.error("导入QA-未知异常", e);
            throw new BizException(ResponseEnum.PARAM_ERROR.getCode(), "导入QA失败-未知异常");
        } finally {
            cleanupImportFile(importFile);
        }
    }

    /**
     * 生成导入文件路径
     */
    private String generateImportFilePath(String projectId) {
        return "/export/importFile/" + projectId + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN);
    }

    /**
     * 下载并校验Excel文件
     */
    private File downloadAndValidateExcelFile(String fileUrl, String filePath) {
        try {
            FileUtil.downloadUrlToFile(fileUrl, filePath);
        } catch (Exception e) {
            log.error("下载导入文件失败", e);
            throw new BizException("下载导入文件失败");
        }
        File importFile = new File(filePath);

        // 统计行数,校验是否超限制
        EasyExcel.read(importFile, QARow.class, new CountExcelListener())
                .excelType(ExcelTypeEnum.XLSX)
                .headRowNumber(7)
                .sheet().doRead();

        return importFile;
    }

    /**
     * 解析Excel数据并校验
     */
    private ValidateQaListener parseExcelData(File importFile) {
        ValidateQaListener validateQaListener = new ValidateQaListener();
        EasyExcel.read(importFile, QARow.class, validateQaListener)
                .excelType(ExcelTypeEnum.XLSX)
                .headRowNumber(7)
                .sheet().doRead();

        List<String> errorMessages = validateQaListener.getErrorMessages();
        if (CollectionUtil.isNotEmpty(errorMessages)) {
            log.warn("导入QA失败,错误信息{}", JSON.toJSONString(errorMessages));
            throw new BizException(ResponseEnum.PARAM_ERROR.getCode(), "导入QA失败,错误信息:" + JSON.toJSONString(errorMessages));
        }

        return validateQaListener;
    }

    /**
     * 构建问题与ID的映射关系
     */
    private Map<String, String> buildQuestionIdMap(String projectId, Set<String> questionSet) {
        List<String> questionMd5List = questionSet.stream().map(SecureUtil::md5).toList();
        return aiKnowQaMapper.selectByProjectIdAndMd5List(projectId, questionMd5List)
                .stream().collect(Collectors.toMap(AiKnowQaPo::getQuestion, AiKnowQaPo::getQaNo));
    }

    /**
     * 校验去重策略
     */
    private DeDuplicateEnum validateDeDuplicateStrategy(String deDuplicateType) {
        DeDuplicateEnum deDuplicateEnum = DeDuplicateEnum.getByCode(deDuplicateType);
        if (deDuplicateEnum == null) {
            throw new BizException("不支持的去重策略: " + deDuplicateType);
        }
        return deDuplicateEnum;
    }

    /**
     * 清理导入文件
     */
    private void cleanupImportFile(File importFile) {
        if (importFile != null) {
            boolean delete = importFile.delete();
            log.info("删除导入QA文件结果:{}", delete);
        }
    }

    /**
     * 批量处理QA数据
     */
    private void processBatchQAData(ImportQACommand req, Map<String, List<QARow>> groupIdMap,
                                    Map<String, String> questionAndIdMap, DeDuplicateEnum deDuplicateEnum) {
        groupIdMap.forEach((groupNo, qaList) -> {
            List<AddQACommand> batchAddCommands = new ArrayList<>();
            List<EditQACommand> batchEditCommands = new ArrayList<>();

            // 分类处理新增和更新的QA
            classifyQAOperations(req, qaList, groupNo, questionAndIdMap, deDuplicateEnum, batchAddCommands, batchEditCommands);

            // 执行批量添加
            executeBatchAdd(req, groupNo, batchAddCommands);

            // 执行批量更新
            executeBatchEdit(req, groupNo, batchEditCommands);
        });
    }

    /**
     * 分类QA操作（新增或更新）
     */
    private void classifyQAOperations(ImportQACommand req, List<QARow> qaList, String groupNo,
                                      Map<String, String> questionAndIdMap, DeDuplicateEnum deDuplicateEnum,
                                      List<AddQACommand> batchAddCommands, List<EditQACommand> batchEditCommands) {
        for (QARow qa : qaList) {
            String existingQaNo = questionAndIdMap.get(qa.getQuestion());

            if (existingQaNo != null) {
                // 问题已存在，根据策略处理
                if (deDuplicateEnum == DeDuplicateEnum.COVER) {
                    // 覆盖策略：更新现有问题
                    EditQACommand editCommand = EditQACommand.builder()
                            .projectId(req.getProjectId())
                            .qaNo(existingQaNo)
                            .groupNo(groupNo)
                            .question(qa.getQuestion())
                            .answer(qa.getAnswer())
                            .similarQuestionList(qa.getSimilarQuestionList())
                            .operator(req.getOperator())
                            .build();
                    batchEditCommands.add(editCommand);
                }
                // 跳过策略：不做任何处理
            } else {
                // 新问题，添加到批量添加列表
                AddQACommand addCommand = AddQACommand.builder()
                        .projectId(req.getProjectId())
                        .question(qa.getQuestion())
                        .answer(qa.getAnswer())
                        .groupNo(groupNo)
                        .similarQuestionList(qa.getSimilarQuestionList())
                        .operator(req.getOperator())
                        .build();
                batchAddCommands.add(addCommand);
            }
        }
    }

    /**
     * 执行批量添加操作
     */
    private void executeBatchAdd(ImportQACommand req, String groupNo, List<AddQACommand> batchAddCommands) {
        if (CollectionUtil.isNotEmpty(batchAddCommands)) {
            BatchAddQACommand batchAddCommand = BatchAddQACommand.builder()
                    .projectId(req.getProjectId())
                    .qaList(batchAddCommands)
                    .operator(req.getOperator())
                    .build();
            this.batchAddQA(batchAddCommand);
            log.info("批量导入分组 {} 新增QA数量: {}", groupNo, batchAddCommands.size());
        }
    }

    /**
     * 执行批量更新操作
     */
    private void executeBatchEdit(ImportQACommand req, String groupNo, List<EditQACommand> batchEditCommands) {
        if (CollectionUtil.isNotEmpty(batchEditCommands)) {
            BatchEditQACommand batchEditCommand = BatchEditQACommand.builder()
                    .projectId(req.getProjectId())
                    .qaList(batchEditCommands)
                    .operator(req.getOperator())
                    .build();
            this.batchEditQA(batchEditCommand);
            log.info("批量导入分组 {} 覆盖QA数量: {}", groupNo, batchEditCommands.size());
        }
    }

    private Map<String, List<QARow>> processGroup(ImportQACommand req, Map<String, List<QARow>> groupMap, ProjectPo projectPo) {

        //批量查询分组,得到分组ID,如果分组不存在,则要创建分组
        List<String> groupNameList = new ArrayList<>(groupMap.keySet());
        //已存在的分组名称和ID的映射
        Map<String, String> existGroupIdNameMap = aiKnowGroupMapper.selectByGroupNames(req.getProjectId(), groupNameList)
                .stream().collect(Collectors.toMap(AiKnowGroupPo::getGroupName, AiKnowGroupPo::getGroupNo));
        //找到不存在的分组名称,并创建这些分组
        groupNameList.stream()
                .filter(group -> !existGroupIdNameMap.containsKey(group))
                .forEach(newGroup -> {
                    String sequenceNo = SequenceNoUtils.getSequenceNo();
                    AiKnowGroupPo aiKnowGroupPo = new AiKnowGroupPo();
                    aiKnowGroupPo.setGroupNo(sequenceNo);
                    aiKnowGroupPo.setProjectId(req.getProjectId());
                    aiKnowGroupPo.setProjectName(projectPo.getProjectName());
                    aiKnowGroupPo.setGroupName(newGroup);
                    aiKnowGroupPo.setCreator(req.getOperator());
                    aiKnowGroupMapper.insert(aiKnowGroupPo);
                    existGroupIdNameMap.put(newGroup, sequenceNo);
                });
        //将分组名-qa列表的映射转为分组ID-qa列表
        Map<String, List<QARow>> groupIdMap = new HashMap<>();
        groupMap.forEach((groupName, qaList) -> {
            String groupId = existGroupIdNameMap.get(groupName);
            groupIdMap.put(groupId, qaList);
        });
        return groupIdMap;
    }

    @Override
    @OpenLog("导出QA")
    public ExportQABo exportQA(PageQAQuery req) {
        req.setPageNum(1);
        req.setPageSize(10000);
        PageResult<PageQueryQABo> pageResult = this.pageQuery(req);
        List<PageQueryQABo> qaBoList = pageResult.getList();
        if (CollectionUtil.isEmpty(qaBoList)) {
            log.warn("待导出数据为空");
            throw new BizException("待导出数据为空");
        }

        //查询相似问题列表
        List<String> qaNoList = qaBoList.stream().map(PageQueryQABo::getQaNo).toList();
        List<AiKnowSimilarQuestionPo> similarQuestionPoList = aiKnowSimilarQuestionMapper.selectByQaList(req.getProjectId(), qaNoList);
        Map<String, List<AiKnowSimilarQuestionPo>> similarMap = similarQuestionPoList.stream().collect(Collectors.groupingBy(AiKnowSimilarQuestionPo::getQaNo));

        //查询分组名称
        List<AiKnowGroupPo> aiKnowGroupPos = aiKnowGroupMapper.selectByProjectId(req.getProjectId());
        Map<String, String> groupMap = aiKnowGroupPos.stream().collect(Collectors.toMap(AiKnowGroupPo::getGroupNo, AiKnowGroupPo::getGroupName));

        //组装行数据
        List<QARow> rowList = qaBoList.stream().map(qa -> {
            QARow row = new QARow();
            row.setGroupName(groupMap.get(qa.getGroupNo()));
            row.setQuestion(qa.getQuestion());
            row.setAnswer(qa.getAnswer());
            String similarQuestion = similarMap.getOrDefault(qa.getQaNo(), new ArrayList<>()).stream()
                    .map(AiKnowSimilarQuestionPo::getSimilarQuestion)
                    .collect(Collectors.joining("\n"));
            row.setSimilarQuestion(similarQuestion);
            return row;
        }).toList();
        log.info("导出QA数{}", rowList.size());
        //导出至Excel并上传至OSS
        String excelUrl;
        File tempFile = null;
        try {

            String fileKey = "问答话术库导出记录" + DateUtil.format(new Date(), PURE_DATE_FORMAT) + ".xlsx";
            tempFile = File.createTempFile(fileKey, ".xlsx");
            log.info("创建了临时文件{}存储excel,位于{}", fileKey, tempFile.getAbsolutePath());
            EasyExcel.write(tempFile.getPath(), QARow.class)
                    .sheet("问答话术库").doWrite(rowList);

            ossService.uploadFile(fileKey, tempFile);
            excelUrl = ossService.getFileUrl(fileKey);

        } catch (Exception e) {
            log.error("导出已复核违规列表失败,入参{}", JSON.toJSONString(req), e);
            throw new BizException("导出问答话术库失败");
        } finally {
            if (tempFile != null) {
                boolean delete = tempFile.delete();
                log.info("删除临时文件Excel:{}", delete ? "成功" : "失败");
            }
        }
        return new ExportQABo(excelUrl);
    }

    @Override
    @OpenLog("分页查询QA")
    public PageResult<PageQueryQABo> pageQuery(PageQAQuery req) {
        PageQueryQAPo pageQueryQAPo = Converter.INSTANCE.to(req);
        pageQueryQAPo.setKeyword(req.getKeyword());
        PageResult<AiKnowQaPo> pageResult = DBPageUtils.pageQuery(pageQueryQAPo, aiKnowQaMapper::page);
        return PageResult.map(pageResult, Converter.INSTANCE::to1);
    }

    @Override
    @OpenLog("批量更新QA分组")
    public void batchMoveQa(BatchMoveQaCommand req) {
        AiKnowGroupPo aiKnowGroupPo = getAiKnowGroupPo(req.getProjectId(), req.getGroupNo());
        AiKnowQaPo updatePo = new AiKnowQaPo();
        updatePo.setGroupNo(aiKnowGroupPo.getGroupNo());
        updatePo.setModifier(req.getOperator());
        aiKnowQaMapper.updateByQaNoList(updatePo, req.getQaNoList());
    }

    @Override
    @OpenLog("批量删除QA")
    public void batchDeleteQa(BatchDeleteQaCommand req) {

        String projectId = req.getProjectId();
        List<String> qaNoList = req.getQaNoList();

        //按照分组删除或按照问题删除
        List<AiKnowQaPo> list;
        if (CollectionUtil.isNotEmpty(qaNoList)) {
            list = aiKnowQaMapper.selectByQaNoList(projectId, req.getQaNoList());
        }else {
            list = aiKnowQaMapper.selectByGroupNo(req.getGroupNo());
        }

        if (CollectionUtil.isEmpty(list)) {
            log.warn("QA不存在,无需删除");
            return;
        }

        transactionTemplate.executeWithoutResult(transactionStatus -> {
            //批量删除本地QA
            aiKnowQaMapper.logicDeleteByQaNoList(projectId, qaNoList,req.getOperator());

            //批量删除本地相似问题
            aiKnowSimilarQuestionMapper.logicDeleteByQaNo(qaNoList,req.getOperator());
        });

        //异步删除远程QA
        CompletableFuture.runAsync(() -> deleteOnRemote(req, projectId, list));
    }

    private void deleteOnRemote(BatchDeleteQaCommand req, String projectId, List<AiKnowQaPo> list) {

        log.info("开始异步删除项目{}的远程QA,数量: {}", projectId, list.size());
        KnowledgeBaseIdConfig config = getAiKnowConfigPos(projectId);
        if (StrUtil.isBlank(config.getQuestionId()) || StrUtil.isBlank(config.getQaId())) {
            log.error("知识库ID不存在,无法删除QA,入参{}", JSON.toJSONString(req));
            throw new BizException("删除QA失败");
        }

        try {
            for (AiKnowQaPo aiKnowQaPo : list) {
                //删除问题文档
                difyDatasetsClient.deleteDocument(config.getQuestionId(), aiKnowQaPo.getExternalQuestionId());
                //删除QA文档
                difyDatasetsClient.deleteDocument(config.getQaId(), aiKnowQaPo.getExternalQaId());
            }
        } catch (Exception e) {
            log.error("删除QA失败", e);
            throw new BizException("删除QA失败");
        }
    }

}
