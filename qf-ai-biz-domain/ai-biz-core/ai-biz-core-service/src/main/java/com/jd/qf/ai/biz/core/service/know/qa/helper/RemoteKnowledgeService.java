package com.jd.qf.ai.biz.core.service.know.qa.helper;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.biz.core.api.know.qa.bo.AddQACommand;
import com.jd.qf.ai.biz.core.api.know.qa.bo.EditQACommand;
import com.jd.qf.ai.biz.core.service.know.qa.QAEditProcessData;
import com.jd.qf.ai.biz.core.service.know.qa.QAProcessData;
import com.jd.qf.ai.biz.core.api.know.qa.bo.KnowledgeBaseIdConfig;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.AiKnowQaMapper;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo;
import com.jd.qf.ai.server.common.pojo.enums.KnowContentTypeEnum;
import com.jd.qf.ai.server.common.pojo.enums.dify.*;
import com.jdt.open.exception.BizException;
import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.model.datasets.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 远程知识库服务
 * 
 * <AUTHOR>
 * @description 负责与远程知识库的交互操作
 * @date 2025/1/15
 */
@Slf4j
@Service
public class RemoteKnowledgeService {

    @Autowired
    private DifyDatasetsClient difyDatasetsClient;
    
    @Autowired
    private AiKnowQaMapper aiKnowQaMapper;

    /**
     * 问题前缀
     */
    private static final String QUESTION_PREFIX = "问题:";

    /**
     * 答案前缀
     */
    private static final String ANSWER_PREFIX = "答案:";

    /**
     * 换行符
     */
    private static final String SWITCH_LINE = "\n";

    /**
     * 批量添加到远程知识库
     * 
     * @param knowledgeBaseIdConfig 知识库配置
     * @param processDataList 处理数据列表
     */
    public void batchAddToRemoteKnowledge(KnowledgeBaseIdConfig knowledgeBaseIdConfig, List<QAProcessData> processDataList) {
        if (CollectionUtil.isEmpty(processDataList)) {
            return;
        }

        // 使用并行流提高远程API调用效率
        List<CompletableFuture<Void>> futures = processDataList.parallelStream()
                .map(processData -> CompletableFuture.runAsync(() -> {
                    try {
                        addSingleQAToRemote(knowledgeBaseIdConfig, processData);
                    } catch (Exception e) {
                        log.error("批量添加到远程知识库失败，问题: {}", processData.getAddQACommand().getQuestion(), e);
                        throw new RuntimeException("添加到远程知识库失败: " + processData.getAddQACommand().getQuestion(), e);
                    }
                }))
                .toList();

        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("批量添加到远程知识库异步任务执行失败", e);
            throw new BizException("批量添加到远程知识库失败");
        }

        // 批量更新本地库的外部文档ID
        updateLocalExternalIds(processDataList);

        log.info("批量添加到远程知识库完成，数量: {}", processDataList.size());
    }

    /**
     * 批量更新远程知识库
     * 
     * @param knowledgeBaseIdConfig 知识库配置
     * @param processDataList 编辑处理数据列表
     */
    public void batchUpdateRemoteKnowledge(KnowledgeBaseIdConfig knowledgeBaseIdConfig, List<QAEditProcessData> processDataList) {
        if (CollectionUtil.isEmpty(processDataList)) {
            return;
        }

        validateKnowledgeBaseConfig(knowledgeBaseIdConfig);

        // 使用并行流提高远程API调用效率
        List<CompletableFuture<Void>> futures = processDataList.parallelStream()
                .map(processData -> CompletableFuture.runAsync(() -> {
                    try {
                        updateSingleQAInRemote(knowledgeBaseIdConfig, processData);
                    } catch (Exception e) {
                        log.error("批量更新远程知识库失败，问题: {}", processData.getEditCommand().getQuestion(), e);
                        throw new RuntimeException("更新远程知识库失败: " + processData.getEditCommand().getQuestion(), e);
                    }
                }))
                .toList();

        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        } catch (Exception e) {
            log.error("批量更新远程知识库异步任务执行失败", e);
            throw new BizException("批量更新远程知识库失败");
        }

        log.info("批量更新远程知识库完成，数量: {}", processDataList.size());
    }

    /**
     * 添加单个QA到远程知识库
     */
    private void addSingleQAToRemote(KnowledgeBaseIdConfig knowledgeBaseIdConfig, QAProcessData processData) {
        AddQACommand qaCommand = processData.getAddQACommand();

        // 构建问题内容
        StringBuilder externalQuestion = buildQuestionContent(qaCommand.getQuestion(), qaCommand.getSimilarQuestionList());

        // 添加到远程问题库
        String externalQuestionId = addDocumentToKnowledge(qaCommand, knowledgeBaseIdConfig.getQuestionId(), 
                                                           externalQuestion, KnowContentTypeEnum.QUESTION);

        // 构建QA内容
        externalQuestion.append(SWITCH_LINE).append(ANSWER_PREFIX).append(qaCommand.getAnswer());
        String externalQaId = addDocumentToKnowledge(qaCommand, knowledgeBaseIdConfig.getQaId(), 
                                                     externalQuestion, KnowContentTypeEnum.QA);

        // 设置外部ID
        processData.setExternalQuestionId(externalQuestionId);
        processData.setExternalQaId(externalQaId);
    }

    /**
     * 更新单个QA到远程知识库
     */
    private void updateSingleQAInRemote(KnowledgeBaseIdConfig knowledgeBaseIdConfig, QAEditProcessData processData) {
        EditQACommand editCommand = processData.getEditCommand();
        AiKnowQaPo existingQa = processData.getExistingQa();

        // 构建问题内容
        StringBuilder externalQuestion = buildQuestionContent(editCommand.getQuestion(), editCommand.getSimilarQuestionList());

        // 更新远程问题库
        UpdateDocumentByTextRequest updateRequest = new UpdateDocumentByTextRequest();
        updateRequest.setName(editCommand.getQuestion());
        updateRequest.setText(externalQuestion.toString());
        try {
            difyDatasetsClient.updateDocumentByText(knowledgeBaseIdConfig.getQuestionId(), existingQa.getExternalQuestionId(), updateRequest);
        } catch (Exception e) {
            log.error("更新远程问题库失败,入参{}", JSON.toJSONString(editCommand), e);
            throw new BizException("更新远程问题库失败");
        }

        // 构建QA内容并更新QA库
        externalQuestion.append(SWITCH_LINE).append(ANSWER_PREFIX).append(editCommand.getAnswer());
        updateRequest.setText(externalQuestion.toString());
        try {
            difyDatasetsClient.updateDocumentByText(knowledgeBaseIdConfig.getQaId(), existingQa.getExternalQaId(), updateRequest);
        } catch (IOException e) {
            log.error("更新远程QA库失败,入参{}", JSON.toJSONString(editCommand), e);
            throw new BizException("更新远程QA库失败");
        }
    }

    /**
     * 构建问题内容
     */
    private StringBuilder buildQuestionContent(String mainQuestion, List<String> similarQuestionList) {
        StringBuilder content = new StringBuilder(QUESTION_PREFIX + mainQuestion + SWITCH_LINE);
        
        if (CollectionUtil.isNotEmpty(similarQuestionList)) {
            for (String similarQuestion : similarQuestionList) {
                content.append(QUESTION_PREFIX).append(similarQuestion).append(SWITCH_LINE);
            }
        }
        
        return content;
    }

    /**
     * 添加文档到知识库
     */
    private String addDocumentToKnowledge(AddQACommand req, String knowledgeBaseId, StringBuilder content, 
                                          KnowContentTypeEnum knowContentTypeEnum) {
        if (StrUtil.isBlank(knowledgeBaseId)) {
            log.error("项目下不存在知识库配置,入参{}", JSON.toJSONString(req));
            throw new BizException("项目下不存在知识库配置");
        }

        try {
            CreateDocumentByTextRequest textRequest = buildCreateDocumentRequest(req.getQuestion(), content.toString());
            DocumentResponse response = difyDatasetsClient.createDocumentByText(knowledgeBaseId, textRequest);
            return response.getDocument().getId();
        } catch (Exception e) {
            log.error("添加QA到远程知识库失败,入参{}", JSON.toJSONString(req), e);
            throw new BizException("添加问题到远程知识库失败");
        }
    }

    /**
     * 构建创建文档请求
     */
    private CreateDocumentByTextRequest buildCreateDocumentRequest(String questionName, String content) {
        return CreateDocumentByTextRequest.builder()
                .name(questionName)
                .text(content)
                .indexingTechnique(IndexTechniqueEnum.HIGH_QUALITY.getCode())
                .docForm(DocFormEnum.TEXT_MODEL.getCode())
                .docLanguage(DocLanguageEnum.CHINESE.getCode())
                .processRule(ProcessRule.builder()
                        .mode(ModeEnum.CUSTOM.getCode())
                        .rules(ProcessRule.Rules.builder()
                                .preProcessingRules(new ArrayList<>())
                                .segmentation(ProcessRule.Segmentation.builder().maxTokens(4000).separator("\n\n").build())
                                .build())
                        .build())
                .embeddingModel("bge-base-zh-v1.5")
                .embeddingModelProvider("langgenius/xinference/xinference")
                .retrievalModel(RetrievalModel.builder()
                        .searchMethod("hybrid_search")
                        .topK(2)
                        .rerankingModel(RetrievalModel.RerankingModel.builder()
                                .rerankingModelName("bge-reranker-v2-m3")
                                .rerankingProviderName("langgenius/xinference/xinference")
                                .build())
                        .rerankingEnable(true)
                        .scoreThresholdEnabled(true)
                        .scoreThreshold(0.4f)
                        .build())
                .build();
    }

    /**
     * 批量更新本地库的外部文档ID
     */
    private void updateLocalExternalIds(List<QAProcessData> processDataList) {
        List<AiKnowQaPo> updatePoList = processDataList.stream()
                .map(processData -> AiKnowQaPo.builder()
                        .qaNo(processData.getQaPo().getQaNo())
                        .externalQuestionId(processData.getExternalQuestionId())
                        .externalQaId(processData.getExternalQaId())
                        .build())
                .toList();

        // 批量更新外部ID
        for (AiKnowQaPo updatePo : updatePoList) {
            aiKnowQaMapper.updateByQaNo(updatePo);
        }
    }

    /**
     * 校验知识库配置
     */
    private void validateKnowledgeBaseConfig(KnowledgeBaseIdConfig knowledgeBaseIdConfig) {
        if (StrUtil.isBlank(knowledgeBaseIdConfig.getQuestionId()) || StrUtil.isBlank(knowledgeBaseIdConfig.getQaId())) {
            log.warn("远程知识库配置不存在");
            throw new BizException("远程知识库配置不存在");
        }
    }
}
