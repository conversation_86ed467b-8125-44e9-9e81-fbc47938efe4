package com.jd.qf.ai.biz.core.api.know.qa.bo;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量移动QA分组请求
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Data
@Builder
public class BatchDeleteQaCommand {

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * qa编码列表
     */
    private List<String> qaNoList;

    /**
     * 分组编码
     */
    private String groupNo;

    /**
     * 操作人
     */
    private String operator;
}
