package com.jd.qf.ai.biz.core.api.know.qa;

import com.jd.qf.ai.biz.core.api.know.qa.bo.*;
import com.jd.qf.ai.server.common.pojo.page.PageResult;
import com.jd.qf.ai.server.common.pojo.resp.BizResponse;

/**
 * 知识库服务
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
public interface KnowledgeBaseService {

    /**
     * 创建知识库
     */
    void createKnowledgeBase(CreateKnowledgeBaseCommand req);

    /**
     * 添加单条QA
     */
    void addQA(AddQACommand req);

    /**
     * 批量添加QA
     */
    void batchAddQA(BatchAddQACommand req);

    /**
     * 查询QA详情
     */
    QADetailBo queryQADetail(QueryQADetailQuery req);

    /**
     * 编辑QA
     */
    void editQA(EditQACommand req);

    /**
     * 批量编辑QA
     */
    void batchEditQA(BatchEditQACommand req);

    /**
     * 导入QA
     */
    void importQA(ImportQACommand req);

    /**
     * 批量导出
     */
    ExportQABo exportQA(PageQAQuery req);

    /**
     * 分页搜索
     */
    PageResult<PageQueryQABo> pageQuery(PageQAQuery req);

    /**
     * 批量移动分组
     */
    void batchMoveQa(BatchMoveQaCommand req);

    /**
     * 批量删除QA
     */
    void batchDeleteQa(BatchDeleteQaCommand req);

    /**
     * 获取知识库ID配置
     */
    KnowledgeBaseIdConfig getAiKnowConfigPos(String projectId);

    /**
     * 获取相似问题列表
     */
    SimilarQuestionBo getSimilarQuestions(GetSimilarQuestionsQuery req);
}
