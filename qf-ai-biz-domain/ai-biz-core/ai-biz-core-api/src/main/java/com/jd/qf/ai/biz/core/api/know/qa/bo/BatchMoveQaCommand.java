package com.jd.qf.ai.biz.core.api.know.qa.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 批量移动QA分组请求
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Data
public class BatchMoveQaCommand {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不得为空")
    private String projectId;

    /**
     * 新分组编码
     */
    @NotBlank(message = "新分组编码不得为空")
    private String groupNo;

    /**
     * qa编码列表
     */
    private List<String> qaNoList;

    @NotBlank(message = "操作人不能为空")
    private String operator;
}
