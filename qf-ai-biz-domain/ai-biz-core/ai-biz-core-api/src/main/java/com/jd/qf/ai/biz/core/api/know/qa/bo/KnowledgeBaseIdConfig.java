package com.jd.qf.ai.biz.core.api.know.qa.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库ID配置
 * <AUTHOR>
 * @description
 * @date 2025/5/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KnowledgeBaseIdConfig {

    /**
     * 问题库ID
     */
    private String questionId;

    /**
     * qa库Id
     */
    private String qaId;

}
