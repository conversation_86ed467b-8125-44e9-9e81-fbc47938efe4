package com.jd.qf.ai.biz.api.knowledgebase.qa.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量移动QA分组请求
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Data
public class BatchDeleteQaReq {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不得为空")
    private String projectId;

    /**
     * qa编码列表
     */
    @NotEmpty(message = "qa编码列表不得为空")
    private List<String> qaNoList;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
