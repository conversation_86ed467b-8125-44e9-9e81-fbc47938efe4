package com.jd.qf.ai.biz.api.knowledgebase.qa.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 添加QA请求
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Data
public class ImportQAReq {

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不得为空")
    private String projectId;

    /**
     * 导入文件的URL
     */
    @NotBlank(message = "导入文件的URL不得为空")
    private String fileUrl;

    /**
     * 相同问题跳过还是覆盖
     * COVER-覆盖;CONTINUE-跳过
     */
    @NotNull(message = "去重类型不得为空")
    private String deDuplicateType;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;

}
