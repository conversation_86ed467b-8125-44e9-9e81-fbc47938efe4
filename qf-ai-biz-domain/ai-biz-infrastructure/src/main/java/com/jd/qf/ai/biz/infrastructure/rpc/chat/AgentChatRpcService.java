package com.jd.qf.ai.biz.infrastructure.rpc.chat;

import com.jd.qf.ai.server.common.pojo.resp.CommonStreamResponse;
import com.jd.qf.ai.server.sdk.request.ChatReq;
import com.jd.qf.ai.server.sdk.request.GeneralIntentReq;
import com.jd.qf.ai.server.sdk.response.ChatResp;
import com.jd.qf.ai.server.sdk.response.GeneralIntentResp;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * AI聊天RPC服务
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
public interface AgentChatRpcService {

    /**
     * 流式聊天
     */
    Flux<CommonStreamResponse> streamChat(ChatReq req);

    /**
     * 意图识别
     */
    Mono<GeneralIntentResp> generalIntent(GeneralIntentReq req);

    /**
     * 阻塞聊天
     */
    Mono<ChatResp> chat(ChatReq req);
}
