package com.jd.qf.ai.biz.infrastructure.dao.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * AI聊天记录表
 */
@Data
public class AiChatRecordPo {
    /**
     * 自增主键
     */
    private Long id;
    /**
     * 记录编号
     */
    private String recordNo;
    /**
     * 企业ID
     */
    private String corpId;
    /**
     * 项目ID
     */
    private String projectId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 使用的AgentId
     */
    private String agentId;
    /**
     * 消息ID
     */
    private String msgRecordId;

    /**
     * 消息时间
     */
    private Date msgTime;
    /**
     * 员工ID
     */
    private Integer sysUserId;
    /**
     * 员工名称
     */
    private String sysUserName;
    /**
     * 客户ID
     */
    private String custId;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 群聊ID
     */
    private String groupId;
    /**
     * 消息内容
     */
    private String msgText;
    /**
     * AI回复
     */
    private String aiAnswer;
    /**
     * 消息上下文，map结构的json，带给大模型侧
     */
    private String msgContext;
    /**
     * 记录状态：初始化-INIT;不回复-NO_REPLY;执行中-PROCESSING;已完成-COMPLETED
     */
    private String dataStatus;
    /**
     * 采纳状态：已采纳-ACCEPTED;未采纳-NOT_ACCEPTED;反对-OPPOSED
     */
    private String acceptStatus;
    /**
     * 不回复原因
     */
    private String noApplyReason;
    /**
     * 逻辑删除标志,1:正常记录,0:已逻辑删除
     */
    private Integer valid;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 最后更新人
     */
    private String modifier;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedTime;


    /**
     * 旧数据状态
     */
    private String oldDataStatus;

    /**
     * 旧的接受状态
     */
    private String oldAcceptStatus;

    /**
     * 场景：FANS-私聊；GROUP-群聊
     */
    private String fansType;

    /**
     * 调用方式:STREAM-流式调用；INVOKE-阻塞调用
     */
    private String invokeType;

    /**
     * 会话状态:IN_PROGRESS-进行中;SOP_END-SOP结束
     */
    private String sessionState;

}
