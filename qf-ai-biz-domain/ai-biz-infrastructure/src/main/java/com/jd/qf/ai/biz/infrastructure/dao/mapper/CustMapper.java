package com.jd.qf.ai.biz.infrastructure.dao.mapper;

import com.jd.qf.ai.biz.infrastructure.dao.po.CustPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.ProjectPo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
public interface CustMapper {

    CustPo queryByCustId(@Param("custId") String custId);

    CustPo queryByGroupMember(@Param("groupId") String groupId, @Param("wxCustId") String wxCustId);
}
