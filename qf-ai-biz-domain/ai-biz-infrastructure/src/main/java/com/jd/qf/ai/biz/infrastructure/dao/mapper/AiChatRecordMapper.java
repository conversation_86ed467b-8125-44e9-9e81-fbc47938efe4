package com.jd.qf.ai.biz.infrastructure.dao.mapper;

import com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordQueryPo;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * AI聊天记录表Mapper接口
 */
public interface AiChatRecordMapper {
    
    /**
     * 插入记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int insert(AiChatRecordPo record);

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 记录
     */
    AiChatRecordPo selectById(@Param("id") Long id);

    /**
     * 根据记录编号查询
     *
     * @param recordNo 记录编号
     * @return 记录
     */
    AiChatRecordPo selectByRecordNo(@Param("recordNo") String recordNo);

    /**
     * 根据消息ID查询
     *
     * @param msgRecordId 消息ID
     * @return 记录
     */
    AiChatRecordPo selectByMsgRecordId(@Param("msgRecordId") String msgRecordId);

    /**
     * 更新记录
     *
     * @param record 记录
     * @return 影响行数
     */
    int update(AiChatRecordPo record);

    /**
     * 根据条件查询列表
     *
     * @param record 查询条件
     * @return 记录列表
     */
    List<AiChatRecordPo> selectList(AiChatRecordPo record);


    /**
     * 根据记录编号更新受理状态
     * @param recordNo 记录编号
     * @param acceptStatus 要更新的受理状态
     * @return 返回更新操作影响的行数
     */
    Integer updateAcceptStatusByRecordNo(@Param("recordNo") String recordNo, @Param("acceptStatus") String acceptStatus);

    /**
     * 分页查询AI聊天记录列表
     * @param queryPo 查询条件对象，包含分页参数和筛选条件
     * @return 符合查询条件的AI聊天记录分页列表
     */
    List<AiChatRecordPo> selectListByPage(AiChatRecordQueryPo queryPo);

    List<AiChatRecordPo> selectListByLocation(AiChatRecordQueryPo queryPo);

    void deleteByRecordNo(String recordNo);

    AiChatRecordPo selectLastAiRecord(@Param("custId") String custId);
}
