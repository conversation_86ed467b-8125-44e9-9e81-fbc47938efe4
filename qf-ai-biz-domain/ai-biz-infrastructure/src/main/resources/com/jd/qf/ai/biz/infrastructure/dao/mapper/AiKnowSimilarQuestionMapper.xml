<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.biz.infrastructure.dao.mapper.AiKnowSimilarQuestionMapper">
    <resultMap id="BaseResultMap" type="com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowSimilarQuestionPo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="similar_question_no" jdbcType="VARCHAR" property="similarQuestionNo" />
        <result column="qa_no" jdbcType="VARCHAR" property="qaNo" />
        <result column="similar_question" jdbcType="VARCHAR" property="similarQuestion" />
        <result column="similar_question_md5" jdbcType="VARCHAR" property="similarQuestionMd5" />
        <result column="external_question_id" jdbcType="VARCHAR" property="externalQuestionId" />
        <result column="external_qa_id" jdbcType="VARCHAR" property="externalQaId" />
        <result column="valid" jdbcType="TINYINT" property="valid" />
        <result column="creator" jdbcType="VARCHAR" property="creator" />
        <result column="modifier" jdbcType="VARCHAR" property="modifier" />
        <result column="project_id" jdbcType="VARCHAR" property="projectId" />
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, similar_question_no, qa_no, similar_question, similar_question_md5, external_question_id,
        external_qa_id, valid, creator, modifier, created_time, modified_time,project_id
    </sql>

    <insert id="insert" parameterType="com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowSimilarQuestionPo">
        INSERT INTO ai_know_similar_question (similar_question_no, qa_no, similar_question,
            similar_question_md5, external_question_id, external_qa_id, valid, creator, modifier,project_id)
        VALUES (#{similarQuestionNo}, #{qaNo}, #{similarQuestion},
            #{similarQuestionMd5}, #{externalQuestionId}, #{externalQaId}, #{valid}, #{creator}, #{modifier},#{projectId})
    </insert>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_know_similar_question
        WHERE id = #{id}
    </select>

    <update id="updateByPrimaryKey" parameterType="com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowSimilarQuestionPo">
        UPDATE ai_know_similar_question
        SET similar_question_no = #{similarQuestionNo},
            qa_no = #{qaNo},
            similar_question = #{similarQuestion},
            similar_question_md5 = #{similarQuestionMd5},
            external_question_id = #{externalQuestionId},
            external_qa_id = #{externalQaId},
            valid = #{valid},
            modifier = #{modifier},
            modified_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM ai_know_similar_question
        WHERE id = #{id}
    </delete>

    <select id="selectByQaNoAndMd5" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ai_know_similar_question
        WHERE qa_no = #{qaNo} and similar_question_md5 in
        <foreach collection="similarQuestionMd5List" item="md5" open="(" close=")" separator="or">
                similar_question_md5 = #{md5}
        </foreach>
    </select>

    <select id="selectByQaNo" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM ai_know_similar_question
        WHERE qa_no = #{qaNo} and valid=1
    </select>

    <update id="logicDeleteByQaNo">
        UPDATE ai_know_similar_question
        SET valid = 0,
            modified_time = CURRENT_TIMESTAMP,modifier = #{operator,jdbcType=VARCHAR}
        WHERE qa_no in
        <foreach collection="qaNoList" item="qaNo" open="(" close=")" separator="or">
            qa_no = #{qaNo}
        </foreach>
    </update>

    <select id="selectByQaList" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM ai_know_similar_question
        WHERE valid=1 and qa_no in
        <foreach collection="qaNoList" item="qaNo" open="(" close=")" separator=",">
                #{qaNo}
        </foreach>
        and project_id=#{projectId,jdbcType=VARCHAR}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ai_know_similar_question (similar_question_no, qa_no, similar_question,
                                             similar_question_md5, external_question_id, external_qa_id, valid, creator, modifier, project_id)
        VALUES
        <foreach collection="similarQuestionList" item="item" separator=",">
            (#{item.similarQuestionNo}, #{item.qaNo}, #{item.similarQuestion},
             #{item.similarQuestionMd5}, #{item.externalQuestionId}, #{item.externalQaId}, #{item.valid}, #{item.creator}, #{item.modifier}, #{item.projectId})
        </foreach>
    </insert>
</mapper>
