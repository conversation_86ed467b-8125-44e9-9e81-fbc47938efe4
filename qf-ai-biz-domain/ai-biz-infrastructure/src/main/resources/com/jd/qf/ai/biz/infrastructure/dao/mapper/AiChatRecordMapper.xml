<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.biz.infrastructure.dao.mapper.AiChatRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo">
        <id column="id" property="id"/>
        <result column="record_no" property="recordNo"/>
        <result column="corp_id" property="corpId"/>
        <result column="project_id" property="projectId"/>
        <result column="project_name" property="projectName"/>
        <result column="agent_id" property="agentId"/>
        <result column="msg_record_id" property="msgRecordId"/>
        <result column="msg_time" property="msgTime"/>
        <result column="sys_user_id" property="sysUserId"/>
        <result column="sys_user_name" property="sysUserName"/>
        <result column="cust_id" property="custId"/>
        <result column="cust_name" property="custName"/>
        <result column="group_id" property="groupId"/>
        <result column="msg_text" property="msgText"/>
        <result column="ai_answer" property="aiAnswer"/>
        <result column="msg_context" property="msgContext"/>
        <result column="data_status" property="dataStatus"/>
        <result column="accept_status" property="acceptStatus"/>
        <result column="no_apply_reason" property="noApplyReason"/>
        <result column="valid" property="valid"/>
        <result column="creator" property="creator"/>
        <result column="modifier" property="modifier"/>
        <result column="created_time" property="createdTime"/>
        <result column="modified_time" property="modifiedTime"/>
        <result column="fans_type" property="fansType"/>
        <result column="invoke_type" property="invokeType"/>
        <result column="session_state" property="sessionState"/>
    </resultMap>

    <!-- 所有列 -->
    <sql id="Base_Column_List">
        id, record_no, corp_id, project_id, project_name, agent_id, msg_record_id,msg_time, sys_user_id, sys_user_name,
        cust_id, cust_name, group_id, msg_text, ai_answer, msg_context, data_status, accept_status,
        no_apply_reason, valid, creator, modifier, created_time, modified_time, fans_type, invoke_type, session_state
    </sql>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_chat_record (
        record_no, corp_id, project_id, project_name, agent_id, msg_record_id,msg_time, sys_user_id, sys_user_name,
        cust_id, cust_name, group_id, msg_text, ai_answer, msg_context, data_status, accept_status,
        no_apply_reason, valid, creator, modifier, created_time, modified_time, fans_type, invoke_type, session_state
        )
        VALUES (
        #{recordNo}, #{corpId}, #{projectId}, #{projectName}, #{agentId}, #{msgRecordId},#{msgTime}, #{sysUserId}, #{sysUserName},
        #{custId}, #{custName}, #{groupId}, #{msgText}, #{aiAnswer}, #{msgContext},
        #{dataStatus}, #{acceptStatus}, #{noApplyReason}, #{valid}, #{creator}, #{modifier},
        #{createdTime}, #{modifiedTime}, #{fansType}, #{invokeType}, #{sessionState}
        )
    </insert>

    <!-- 根据主键查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_record
        WHERE id = #{id} AND valid = 1
    </select>

    <!-- 根据记录编号查询 -->
    <select id="selectByRecordNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_record
        WHERE record_no = #{recordNo} AND valid = 1
    </select>

    <!-- 根据消息ID查询 -->
    <select id="selectByMsgRecordId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_record
        WHERE msg_record_id = #{msgRecordId} AND valid = 1
    </select>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo">
        UPDATE ai_chat_record
        <set>
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="aiAnswer != null">ai_answer = #{aiAnswer},</if>
            <if test="msgContext != null">msg_context = #{msgContext},</if>
            <if test="dataStatus != null">data_status = #{dataStatus},</if>
            <if test="acceptStatus != null">accept_status = #{acceptStatus},</if>
            <if test="noApplyReason != null">no_apply_reason = #{noApplyReason},</if>
            <if test="fansType != null">fans_type = #{fansType},</if>
            <if test="invokeType != null">invoke_type = #{invokeType},</if>
            <if test="sessionState != null">session_state = #{sessionState},</if>
            <if test="valid != null">valid = #{valid},</if>
            <if test="modifier != null">modifier = #{modifier},</if>
            modified_time = CURRENT_TIMESTAMP
        </set>
        <where>
            <if test="recordNo != null and recordNo != ''">
                record_no = #{recordNo}
            </if>
            <if test="msgRecordId != null and msgRecordId != ''">
                msg_record_id = #{msgRecordId}
            </if>
            <if test="oldDataStatus != null and oldDataStatus != ''">
                AND data_status = #{oldDataStatus}
            </if>
            <if test="oldAcceptStatus != null and oldAcceptStatus != ''">
                AND accept_status = #{oldAcceptStatus}
            </if>
        </where>
    </update>

    <update id="updateAcceptStatusByRecordNo">
        UPDATE ai_chat_record
        set accept_status = #{acceptStatus},
        modified_time = CURRENT_TIMESTAMP
        WHERE record_no = #{recordNo}
    </update>

    <!-- 条件查询列表 -->
    <select id="selectList" parameterType="com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_record
        <where>
            <if test="corpId != null">AND corp_id = #{corpId}</if>
            <if test="projectId != null">AND project_id = #{projectId}</if>
            <if test="sysUserId != null">AND sys_user_id = #{sysUserId}</if>
            <if test="custId != null">AND cust_id = #{custId}</if>
            <if test="dataStatus != null">AND data_status = #{dataStatus}</if>
            <if test="acceptStatus != null">AND accept_status = #{acceptStatus}</if>
            <if test="msgRecordId != null and msgRecordId != ''">AND msg_record_id = #{msgRecordId}</if>
            AND valid = 1
        </where>
        ORDER BY created_time DESC
    </select>
    <select id="selectListByPage" resultType="com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo">
        SELECT <include refid="Base_Column_List"/>
        from ai_chat_record
        <where>
            <if test="corpId != null">AND corp_id = #{corpId}</if>
            <if test="projectId != null">AND project_id = #{projectId}</if>
            <if test="sysUserId != null">AND sys_user_id = #{sysUserId}</if>
            <if test="custId != null">AND cust_id = #{custId}</if>
        </where>
        order by created_time desc
    </select>

    <select id="selectListByLocation" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from ai_chat_record
        <where>
            <if test="corpId != null">
                AND corp_id = #{corpId}
            </if>
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
            <if test="sysUserId != null">
                AND sys_user_id = #{sysUserId}
            </if>
            <if test="custId != null">
                AND cust_id = #{custId}
            </if>
            <if test="msgRecordIdList != null and msgRecordIdList.size() != 0">
                AND msg_record_id IN
                <foreach item="item" index="index" collection="msgRecordIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="locationMsgRecordId != null and locationMsgRecordId != ''">
                AND msg_record_id != #{locationMsgRecordId,jdbcType=VARCHAR}
            </if>
            <if test="locationMsgTime != null and locationType == 'BEFORE'">
                AND msg_time &lt; #{locationMsgTime}
            </if>
            <if test="locationMsgTime != null and locationType == 'AFTER'">
                AND msg_time &gt; #{locationMsgTime}
            </if>
        </where>
        order by
        <choose>
            <when test="locationType == 'BEFORE'">
                msg_time DESC
            </when>
            <when test="locationType == 'AFTER'">
                msg_time ASC
            </when>
            <otherwise>
                msg_time ASC
            </otherwise>
        </choose>
        limit #{limit}
    </select>

    <delete id="deleteByRecordNo">
        DELETE FROM ai_chat_record
        WHERE record_no = #{recordNo}
    </delete>

    <select id="selectLastAiRecord" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_chat_record
        WHERE cust_id = #{custId} AND valid = 1
        ORDER BY created_time DESC
        LIMIT 1
    </select>
</mapper>
