<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.biz.infrastructure.dao.mapper.AiKnowQaMapper">
    <resultMap id="BaseResultMap" type="com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="group_no" property="groupNo" jdbcType="VARCHAR"/>
        <result column="qa_no" property="qaNo" jdbcType="VARCHAR"/>
        <result column="project_id" property="projectId" jdbcType="VARCHAR"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
        <result column="external_question_id" property="externalQuestionId" jdbcType="VARCHAR"/>
        <result column="external_qa_id" property="externalQaId" jdbcType="VARCHAR"/>
        <result column="question" property="question" jdbcType="VARCHAR"/>
        <result column="question_md5" property="questionMd5" jdbcType="VARCHAR"/>
        <result column="answer" property="answer" jdbcType="VARCHAR"/>
        <result column="valid" property="valid" jdbcType="TINYINT"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="modified_time" property="modifiedTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, group_no, qa_no, project_id, project_name, external_question_id, external_qa_id,
        question, question_md5, answer, valid, creator, modifier, created_time, modified_time
    </sql>

    <insert id="insert" parameterType="com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_know_qa
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupNo != null">group_no,</if>
            <if test="qaNo != null">qa_no,</if>
            <if test="projectId != null">project_id,</if>
            <if test="projectName != null">project_name,</if>
            <if test="externalQuestionId != null">external_question_id,</if>
            <if test="externalQaId != null">external_qa_id,</if>
            <if test="question != null">question,</if>
            <if test="questionMd5 != null">question_md5,</if>
            <if test="answer != null">answer,</if>
            <if test="valid != null">valid,</if>
            <if test="creator != null">creator,</if>
            <if test="modifier != null">modifier,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="modifiedTime != null">modified_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupNo != null">#{groupNo,jdbcType=VARCHAR},</if>
            <if test="qaNo != null">#{qaNo,jdbcType=VARCHAR},</if>
            <if test="projectId != null">#{projectId,jdbcType=VARCHAR},</if>
            <if test="projectName != null">#{projectName,jdbcType=VARCHAR},</if>
            <if test="externalQuestionId != null">#{externalQuestionId,jdbcType=VARCHAR},</if>
            <if test="externalQaId != null">#{externalQaId,jdbcType=VARCHAR},</if>
            <if test="question != null">#{question,jdbcType=VARCHAR},</if>
            <if test="questionMd5 != null">#{questionMd5,jdbcType=VARCHAR},</if>
            <if test="answer != null">#{answer,jdbcType=VARCHAR},</if>
            <if test="valid != null">#{valid,jdbcType=TINYINT},</if>
            <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
            <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="modifiedTime != null">#{modifiedTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo">
        UPDATE ai_know_qa
        <set>
            <if test="groupNo != null">group_no = #{groupNo,jdbcType=VARCHAR},</if>
            <if test="qaNo != null">qa_no = #{qaNo,jdbcType=VARCHAR},</if>
            <if test="projectId != null">project_id = #{projectId,jdbcType=VARCHAR},</if>
            <if test="projectName != null">project_name = #{projectName,jdbcType=VARCHAR},</if>
            <if test="externalQuestionId != null">external_question_id = #{externalQuestionId,jdbcType=VARCHAR},</if>
            <if test="externalQaId != null">external_qa_id = #{externalQaId,jdbcType=VARCHAR},</if>
            <if test="question != null">question = #{question,jdbcType=VARCHAR},</if>
            <if test="questionMd5 != null">question_md5 = #{questionMd5,jdbcType=VARCHAR},</if>
            <if test="answer != null">answer = #{answer,jdbcType=VARCHAR},</if>
            <if test="valid != null">valid = #{valid,jdbcType=TINYINT},</if>
            <if test="modifier != null">modifier = #{modifier,jdbcType=VARCHAR},</if>
            <if test="modifiedTime != null">modified_time = #{modifiedTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_know_qa WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE id = #{id,jdbcType=BIGINT} and valid=1
    </select>

    <select id="selectByQaNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE qa_no = #{qaNo,jdbcType=VARCHAR} and valid=1
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE valid=1
    </select>

    <select id="selectByProjectId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE project_id = #{projectId,jdbcType=VARCHAR} and valid=1
    </select>

    <select id="selectByGroupNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE group_no = #{groupNo,jdbcType=VARCHAR} and valid=1
    </select>

    <select id="selectByProjectIdAndQuestionMd5" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE project_id = #{projectId,jdbcType=VARCHAR} AND question_md5 = #{questionMd5,jdbcType=VARCHAR} and valid=1
    </select>

    <select id="selectByProjectIdAndQaNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        WHERE project_id = #{projectId,jdbcType=VARCHAR} AND qa_no = #{qaNo,jdbcType=VARCHAR} and valid=1
    </select>

    <update id="updateByQaNo">
        UPDATE ai_know_qa
        <set>
            <if test="groupNo != null">group_no = #{groupNo,jdbcType=VARCHAR},</if>
            <if test="qaNo != null">qa_no = #{qaNo,jdbcType=VARCHAR},</if>
            <if test="projectId != null">project_id = #{projectId,jdbcType=VARCHAR},</if>
            <if test="projectName != null">project_name = #{projectName,jdbcType=VARCHAR},</if>
            <if test="externalQuestionId != null">external_question_id = #{externalQuestionId,jdbcType=VARCHAR},</if>
            <if test="externalQaId != null">external_qa_id = #{externalQaId,jdbcType=VARCHAR},</if>
            <if test="question != null">question = #{question,jdbcType=VARCHAR},</if>
            <if test="questionMd5 != null">question_md5 = #{questionMd5,jdbcType=VARCHAR},</if>
            <if test="answer != null">answer = #{answer,jdbcType=VARCHAR},</if>
            <if test="valid != null">valid = #{valid,jdbcType=TINYINT},</if>
            <if test="modifier != null">modifier = #{modifier,jdbcType=VARCHAR},</if>
            <if test="modifiedTime != null">modified_time = #{modifiedTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE qa_no = #{qaNo,jdbcType=VARCHAR}
    </update>

    <update id="logicDeleteByQaNoList">
        UPDATE ai_know_qa
        SET valid = 0,modified_time = CURRENT_TIMESTAMP,modifier = #{operator,jdbcType=VARCHAR}
        WHERE project_id=#{projectId,jdbcType=VARCHAR} and  qa_no IN
        <foreach collection="qaNoList" item="qaNo" open="(" separator="," close=")">
            #{qaNo}
        </foreach>
    </update>

    <update id="updateByQaNoList">
        UPDATE ai_know_qa
        <set>
            <if test="po.groupNo != null and po.groupNo!=''">group_no = #{po.groupNo,jdbcType=VARCHAR},</if>
        </set>
        where valid=1 and qa_no IN
        <foreach collection="qaNoList" item="qaNo" open="(" separator="," close=")">
                #{qaNo}
        </foreach>
    </update>

    <select id="page" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        <where>
            valid=1 and
            <if test="projectId != null and projectId != ''">
                    and project_id = #{projectId,jdbcType=VARCHAR}
            </if>
            <if test=" keyword!= null and keyword != ''">
                    and question like concat('%',#{keyword,jdbcType=VARCHAR},'%')
            </if>
            <if test="groupNo != null and groupNo != ''">
                and group_no=#{groupNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectByQaNoList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE project_id=#{projectId,jdbcType=VARCHAR} and  qa_no IN
        <foreach collection="qaNoList" item="qaNo" open="(" separator="," close=")">
                #{qaNo}
        </foreach>
        and valid=1
    </select>

    <select id="selectByProjectIdAndMd5List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ai_know_qa
        WHERE project_id=#{projectId,jdbcType=VARCHAR} and  question_md5 IN
        <foreach collection="questionMd5List" item="md5" open="(" separator="," close=")">
                #{md5}
        </foreach>
        and valid=1
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ai_know_qa (group_no, qa_no, project_id, project_name, external_question_id, external_qa_id,
                                question, question_md5, answer, valid, creator, modifier)
        VALUES
        <foreach collection="qaList" item="item" separator=",">
            (#{item.groupNo}, #{item.qaNo}, #{item.projectId}, #{item.projectName}, #{item.externalQuestionId}, #{item.externalQaId},
             #{item.question}, #{item.questionMd5}, #{item.answer}, #{item.valid}, #{item.creator}, #{item.modifier})
        </foreach>
    </insert>

    <select id="countByProjectId" resultType="int">
        SELECT count(1)
        FROM ai_know_qa
        WHERE project_id = #{projectId,jdbcType=VARCHAR} and valid=1
    </select>
</mapper>
