<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.biz.infrastructure.dao.mapper.CustMapper">

    <select id="queryByCustId" resultType="com.jd.qf.ai.biz.infrastructure.dao.po.CustPo">
        SELECT
        id as custId,
        name as custName,
        create_time as custCreateTime
        FROM cust
        WHERE id = #{custId,jdbcType=VARCHAR}
    </select>

    <select id="queryByGroupMember" resultType="com.jd.qf.ai.biz.infrastructure.dao.po.CustPo">
        SELECT b.join_time as custCreateTime, b.nickname as custName
        FROM sys_group a
                 JOIN sys_group_rel b on a.id = b.sys_group_id
        WHERE a.room_chat_id = #{groupId,jdbcType=VARCHAR}
          AND b.wx_group_user_id = #{wxCustId,jdbcType=VARCHAR}
    </select>
</mapper>