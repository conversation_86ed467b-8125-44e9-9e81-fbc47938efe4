package com.jd.qf.ai.biz.facade.converter;

import com.jd.qf.ai.biz.api.knowledgebase.qa.dto.*;
import com.jd.qf.ai.biz.api.knowledgebase.qa.req.BatchAddQAReq;
import com.jd.qf.ai.biz.core.api.chat.bo.*;
import com.jd.qf.ai.biz.core.api.know.qa.bo.*;
import com.jd.qf.ai.biz.facade.controller.chat.vo.req.*;
import com.jd.qf.ai.biz.facade.controller.chat.vo.resp.AiChatRecordResp;
import com.jd.qf.ai.biz.facade.controller.chat.vo.resp.IntentResp;
import com.jd.qf.ai.biz.facade.controller.chat.vo.resp.StreamChatResp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

import static org.mapstruct.factory.Mappers.getMapper;

@Mapper
public interface Converter {

    /**
     * 获取Converter接口的实例对象。
     */
    Converter INSTANCE = getMapper(Converter.class);

    UpdateAcceptStatusQuery to(UpdateAcceptStatusReq req);

    AiChatRecordPageQuery to(AiChatRecordPageReq req);

    AiChatRecordResp to(AiChatRecordBo bo);

    List<AiChatRecordResp> toAiChatRecordResp(List<AiChatRecordBo> list);

    IntentResp to(IntentBo resp);

    IntentQuery to(IntentReq req);

    ReplyCommand to(ReplyReq req);

    StreamChatResp to(StreamChatBo streamChatBo);

    AsyncReplyCommand to(AsyncReplyReq req);

    // 知识库相关转换方法
    CreateKnowledgeBaseCommand to(CreateKnowledgeBaseReq req);

    AddQACommand to(AddQAReq req);

    BatchAddQACommand to(BatchAddQAReq req);

    QueryQADetailQuery to(QueryQADetailReq req);

    QADetailResp to(QADetailBo bo);

    EditQACommand to(EditQAReq req);

    ImportQACommand to(ImportQAReq req);

    ImportQACommand toImportCommand(ImportQAReq req);

    PageQAQuery to(PageQueryQAReq req);

    ExportQAResp to(ExportQABo bo);

    PageQueryQAResp to(PageQueryQABo bo);

    List<PageQueryQAResp> toPageQueryQAResp(List<PageQueryQABo> boList);

    BatchMoveQaCommand to(BatchMoveQaReq req);

    BatchDeleteQaCommand to(BatchDeleteQaReq req);

    GetSimilarQuestionsQuery to(GetSimilarQuestionsReq req);

    SimilarQuestionResp to(SimilarQuestionBo bo);
}
