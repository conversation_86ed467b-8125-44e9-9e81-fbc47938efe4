package com.jd.qf.ai.biz.facade.api.know.qa;

import com.jd.qf.ai.biz.api.knowledgebase.qa.KnowledgeBaseFacade;
import com.jd.qf.ai.biz.api.knowledgebase.qa.dto.*;
import com.jd.qf.ai.biz.api.knowledgebase.qa.req.BatchAddQAReq;
import com.jd.qf.ai.biz.core.api.know.qa.KnowledgeBaseService;
import com.jd.qf.ai.biz.core.api.know.qa.bo.*;
import com.jd.qf.ai.biz.facade.converter.Converter;
import com.jd.qf.ai.server.common.pojo.page.PageResult;
import com.jd.qf.ai.server.common.pojo.resp.BizResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 知识库门面实现类
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
@Slf4j
@Service
public class KnowledgeBaseFacadeImpl implements KnowledgeBaseFacade {

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @Override
    public BizResponse<Void> createKnowledgeBase(CreateKnowledgeBaseReq req) {
        CreateKnowledgeBaseCommand command = Converter.INSTANCE.to(req);
        knowledgeBaseService.createKnowledgeBase(command);
        return BizResponse.success();
    }

    @Override
    public BizResponse<Void> addQA(AddQAReq req) {
        AddQACommand command = Converter.INSTANCE.to(req);
        knowledgeBaseService.addQA(command);
        return BizResponse.success();
    }

    @Override
    public BizResponse<Void> batchAddQA(BatchAddQAReq req) {
        BatchAddQACommand command = Converter.INSTANCE.to(req);
        knowledgeBaseService.batchAddQA(command);
        return BizResponse.success();
    }

    @Override
    public BizResponse<QADetailResp> queryQADetail(QueryQADetailReq req) {
        QueryQADetailQuery query = Converter.INSTANCE.to(req);
        QADetailBo bo = knowledgeBaseService.queryQADetail(query);
        QADetailResp resp = Converter.INSTANCE.to(bo);
        return BizResponse.success(resp);
    }

    @Override
    public BizResponse<Void> editQA(EditQAReq req) {
        EditQACommand command = Converter.INSTANCE.to(req);
        knowledgeBaseService.editQA(command);
        return BizResponse.success();
    }

    @Override
    public BizResponse<Void> importQA(ImportQAReq req) {
        ImportQACommand command = Converter.INSTANCE.toImportCommand(req);
        knowledgeBaseService.importQA(command);
        return BizResponse.success();
    }

    @Override
    public BizResponse<ExportQAResp> exportQA(PageQueryQAReq req) {
        PageQAQuery query = Converter.INSTANCE.to(req);
        ExportQABo bo = knowledgeBaseService.exportQA(query);
        ExportQAResp resp = Converter.INSTANCE.to(bo);
        return BizResponse.success(resp);
    }

    @Override
    public BizResponse<PageResult<PageQueryQAResp>> pageQuery(PageQueryQAReq req) {
        PageQAQuery query = Converter.INSTANCE.to(req);
        PageResult<PageQueryQABo> boPageResult = knowledgeBaseService.pageQuery(query);

        // 转换分页结果
        PageResult<PageQueryQAResp> respPageResult = new PageResult<>();
        respPageResult.setTotal(boPageResult.getTotal());
        respPageResult.setList(Converter.INSTANCE.toPageQueryQAResp(boPageResult.getList()));

        return BizResponse.success(respPageResult);
    }

    @Override
    public BizResponse<Void> batchMoveQa(BatchMoveQaReq req) {
        BatchMoveQaCommand command = Converter.INSTANCE.to(req);
        knowledgeBaseService.batchMoveQa(command);
        return BizResponse.success();
    }

    @Override
    public BizResponse<Void> batchDeleteQa(BatchDeleteQaReq req) {
        BatchDeleteQaCommand command = Converter.INSTANCE.to(req);
        knowledgeBaseService.batchDeleteQa(command);
        return BizResponse.success();
    }

    @Override
    public BizResponse<SimilarQuestionResp> getSimilarQuestions(GetSimilarQuestionsReq req) {
        GetSimilarQuestionsQuery query = Converter.INSTANCE.to(req);
        SimilarQuestionBo bo = knowledgeBaseService.getSimilarQuestions(query);
        SimilarQuestionResp resp = Converter.INSTANCE.to(bo);
        return BizResponse.success(resp);
    }
}
