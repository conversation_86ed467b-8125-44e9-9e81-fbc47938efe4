package com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 意图识别请求
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelfBuildBlockRequest {

    /**
     * 输入
     */
    @JsonProperty("input")
    private Map<String,Object> input;

}
