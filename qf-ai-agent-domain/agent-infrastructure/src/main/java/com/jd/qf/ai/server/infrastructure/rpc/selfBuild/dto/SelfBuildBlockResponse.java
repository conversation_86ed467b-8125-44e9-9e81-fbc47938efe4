package com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通用意图识别返回值
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SelfBuildBlockResponse {

    /**
     * 输出
     */
    @JsonProperty("output")
    private String output;

    /**
     * 元数据
     */
    @JsonProperty("metadata")
    private MetaResponse metadata;
}
