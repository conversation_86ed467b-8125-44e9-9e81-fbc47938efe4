package com.jd.qf.ai.server.infrastructure.rpc.selfBuild;

import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 自建Agent RPC服务
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
public interface SelfBuildAgentRpcService {

    /**
     * 意图识别
     */
    Mono<IntentReconResponse> intent(IntentReconRequest request);

    /**
     * 流式聊天
     */
    Flux<SelfBuildStreamResponse> streamChat(SelfBuildStreamRequest request);

    /**
     * 阻塞聊天
     */
    Mono<SelfBuildBlockResponse> chat(SelfBuildBlockRequest request);
}
