package com.jd.qf.ai.agent.core.service.chat.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.agent.core.api.chat.bo.ChatBo;
import com.jd.qf.ai.agent.core.api.chat.bo.ChatQuery;
import com.jd.qf.ai.server.common.pojo.enums.MessageTypeEnum;
import com.jd.qf.ai.server.common.pojo.resp.CommonStreamResponse;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.SelfBuildAgentRpcService;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.SelfBuildBlockRequest;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.SelfBuildStreamRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

import static com.jd.qf.ai.server.common.pojo.constants.ParamConstants.AGENT_ID_PARAM;
import static com.jd.qf.ai.server.common.pojo.constants.ParamConstants.USER_INPUT;

/**
 * 自建Agent服务聊天处理器
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/9
 */
@Slf4j
@Service
public class SelfBuildChatHandler extends AbstractChatHandler {

    @Autowired
    private SelfBuildAgentRpcService selfBuildAgentRpcService;

    @Override
    protected Mono<ChatBo> doChat(ChatQuery request) {
        log.info("自建阻塞聊天请求{}", JSON.toJSONString(request));
        SelfBuildBlockRequest requestDto = new SelfBuildBlockRequest();
        Map<String,Object> inputMap=new HashMap<>();
        inputMap.put(USER_INPUT,request.getAllMessage());
        inputMap.putAll(request.getInputs());
        requestDto.setInput(inputMap);

        return selfBuildAgentRpcService.chat(requestDto)
                .map(resp -> {
                    ChatBo chatBo = new ChatBo();
                    chatBo.setAnswer(resp.getOutput());
                    return chatBo;
                });
    }

    @Override
    protected Flux<CommonStreamResponse> doStreamChat(ChatQuery request) {

        log.info("自建流式聊天请求{}", JSON.toJSONString(request));

        if (CollectionUtil.isEmpty(request.getAllMessage())){
            log.error("自建流式聊天请求,入参allMessage为空,入参为{}", JSON.toJSONString(request));
            return Flux.just(CommonStreamResponse.builder().event(MessageTypeEnum.MESSAGE.getCode()).answer("")
                    .build(),CommonStreamResponse.builder().event(MessageTypeEnum.MESSAGE_END.getCode()).build());
        }

        SelfBuildStreamRequest streamRequest = new SelfBuildStreamRequest();
        streamRequest.setAgentId(request.getAgentId());

        //使用全量对话
        HashMap<String, Object> input = new HashMap<>();
        input.put(USER_INPUT, request.getAllMessage());
        input.put(AGENT_ID_PARAM, request.getAgentId());

        streamRequest.setInput(input);
        return selfBuildAgentRpcService.streamChat(streamRequest)
                .map(resp -> {
                    CommonStreamResponse streamChatResponse = new CommonStreamResponse();
                    streamChatResponse.setAnswer(resp.getContent());
                    streamChatResponse.setId(resp.getId());
                    streamChatResponse.setEvent(MessageTypeEnum.MESSAGE.getCode());
                    return streamChatResponse;
                });
    }
}
