package com.jd.qf.ai.server.sdk.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 意图识别结果
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GeneralIntentResp {

    /**
     * 识别结果:通过-PASS;不通过-FAIL
     * @see com.jd.qf.ai.server.common.pojo.enums.IntentTypeEnum
     */
    private String intentType;

    /**
     * 将要调用的agent标识
     */
    private String agentId;

    /**
     * 调用方式
     * @see com.jd.qf.ai.server.common.pojo.enums.InvokeTypeEnum
     */
    private String invokeType;

    /**
     * 会话状态
     */
    private String sessionState;
}
